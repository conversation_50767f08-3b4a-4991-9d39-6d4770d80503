# 智能分块模块 (Intelligent Chunking)

## 方案说明

### 1. 技术方案概述
智能分块模块负责将提取的文档内容切分为适合向量化和检索的文本块，平衡语义完整性和检索效率，是RAG系统性能的关键环节。该模块采用多策略融合的分块方法，根据文档类型、内容特征和应用场景自动选择最优的分块策略。

### 2. 核心技术特点
- **多策略支持**：固定长度、语义边界、结构化、混合策略等多种分块方法
- **自适应分块**：根据文档特征和内容复杂度动态调整分块参数
- **语义保持**：基于句子边界和语义相关性进行智能切分
- **重叠机制**：支持可配置的块间重叠，提高检索召回率
- **智能优化**：自动优化分块效果，提升检索性能
- **实体感知分块**：基于实体边界和关系完整性的智能分块策略

### 3. 技术架构优势
- **策略可插拔**：基于策略模式设计，支持动态策略选择和扩展
- **性能优化**：并行分块处理，支持大文档高效处理
- **智能优化**：多层次分块优化机制
- **场景适配**：针对不同应用场景提供定制化分块策略

## 流程图

```mermaid
graph TD
    A[结构化文档输入] --> B[文档特征分析]
    B --> C[分块策略选择]

    C --> D{策略类型判断}
    D -->|固定长度| E[固定长度分块]
    D -->|语义边界| F[语义边界分块]
    D -->|结构化| G[结构化分块]
    D -->|混合策略| H[混合策略分块]
    D -->|自适应| I[自适应分块]
    D -->|实体感知| J[实体感知分块]

    E --> J[句子边界检测]
    J --> K[长度控制切分]
    K --> L[重叠处理]

    F --> M[语义相似度计算]
    M --> N[语义边界识别]
    N --> O[语义块生成]

    G --> P[结构元素识别]
    P --> Q[层次结构分析]
    Q --> R[结构化切分]

    H --> S[策略权重计算]
    S --> T[多策略融合]
    T --> U[最优分块选择]

    I --> V[内容复杂度分析]
    V --> W[参数动态调整]
    W --> X[自适应切分]

    J --> Y1[实体边界识别]
    Y1 --> Y2[关系完整性分析]
    Y2 --> Y3[实体感知切分]

    L --> Y[分块优化]
    O --> Y
    R --> Y
    U --> Y
    X --> Y
    Y3 --> Y

    Y --> Z[元数据关联]
    Z --> AA[分块索引构建]
    AA --> BB[标准化输出]

    BB --> CC[向量化模块]

    style A fill:#e1f5fe
    style B fill:#c8e6c9
    style C fill:#fff3e0
```

## 流程步骤说明

### 阶段一：文档分析与策略选择
1. **文档特征分析**：
   - 分析文档长度、结构复杂度、内容类型
   - 识别文档中的标题、段落、表格、列表等结构元素
   - 评估文本的语义密度和主题分布

2. **分块策略选择**：
   - 根据文档特征选择最适合的分块策略
   - 考虑下游应用场景的需求（检索精度vs效率）
   - 动态调整策略参数以适应具体内容

### 阶段二：多策略分块处理
#### 固定长度分块策略
1. **句子边界检测**：使用NLP工具识别句子边界
2. **长度控制**：按照预设的token数量进行切分
3. **边界优化**：在句子边界处进行切分，避免截断语义
4. **重叠处理**：在相邻块之间添加可配置的重叠内容

#### 语义边界分块策略
1. **语义向量计算**：计算句子级别的语义向量
2. **相似度分析**：分析相邻句子的语义相似度
3. **边界识别**：在语义相似度较低的位置进行切分
4. **语义块生成**：生成语义连贯的文本块

#### 结构化分块策略
1. **结构元素识别**：识别标题、段落、表格等结构元素
2. **层次分析**：分析文档的层次结构关系
3. **结构化切分**：按照文档结构进行逻辑分块
4. **上下文保持**：保留必要的上下文信息

#### 混合策略分块
1. **策略权重计算**：根据内容特征计算各策略的权重
2. **多策略执行**：并行执行多种分块策略
3. **结果融合**：融合多种策略的分块结果
4. **最优选择**：选择质量最高的分块方案

#### 自适应分块策略
1. **复杂度分析**：分析内容的语义复杂度和结构复杂度
2. **参数调整**：动态调整分块大小、重叠比例等参数
3. **实时优化**：根据分块效果实时调整策略
4. **自适应切分**：生成最适合当前内容的分块结果

#### 实体感知分块策略
1. **实体边界识别**：识别文档中的实体位置和边界
2. **关系完整性分析**：分析实体间关系的完整性要求
3. **实体感知切分**：在保持实体和关系完整性的前提下进行分块
4. **知识图谱对齐**：确保分块结果与知识图谱结构对齐

### 阶段三：分块优化与输出
1. **分块优化**：
   - 合并过短的分块
   - 拆分过长的分块
   - 优化分块边界
   - 调整重叠内容

2. **元数据关联**：
   - 关联原始文档信息
   - 添加分块位置信息
   - 记录分块策略和参数
   - 记录分块统计信息

3. **索引构建**：
   - 为每个分块分配唯一ID
   - 构建分块间的关联关系
   - 建立分块到原文档的映射

4. **标准化输出**：
   - 封装为标准化的分块对象
   - 准备向量化所需的格式
   - 输出分块统计信息

## 模块概述

智能分块模块负责将提取的文档内容切分为适合向量化和检索的文本块，平衡语义完整性和检索效率，是RAG系统性能的关键环节。

## 核心功能架构

### 1. 多层次分块策略

#### 1.1 分块策略接口设计
```java
public interface ChunkingStrategy {
    List<DocumentChunk> chunk(DocumentContent content, ChunkingConfig config);
    String getStrategyName();
    ChunkingType getType();
    boolean supports(DocumentType documentType);
}

public enum ChunkingType {
    FIXED_SIZE,           // 固定长度分块
    SEMANTIC_BOUNDARY,    // 语义边界分块
    STRUCTURAL,           // 结构化分块
    HYBRID,              // 混合策略
    ADAPTIVE,            // 自适应分块
    ENTITY_AWARE         // 实体感知分块
}
```

#### 1.2 固定长度分块策略
```java
@Component
public class FixedSizeChunkingStrategy implements ChunkingStrategy {
    
    @Override
    public List<DocumentChunk> chunk(DocumentContent content, ChunkingConfig config) {
        List<DocumentChunk> chunks = new ArrayList<>();
        String fullText = extractFullText(content);
        
        int chunkSize = config.getChunkSize();
        int overlap = config.getOverlap();
        
        List<String> sentences = splitIntoSentences(fullText);
        
        int currentLength = 0;
        StringBuilder currentChunk = new StringBuilder();
        List<String> currentSentences = new ArrayList<>();
        
        for (String sentence : sentences) {
            int sentenceLength = getTokenCount(sentence);
            
            if (currentLength + sentenceLength > chunkSize && currentLength > 0) {
                // 创建当前块
                DocumentChunk chunk = createChunk(currentChunk.toString(), currentSentences, chunks.size());
                chunks.add(chunk);
                
                // 处理重叠
                String overlapText = createOverlapText(currentSentences, overlap);
                currentChunk = new StringBuilder(overlapText);
                currentLength = getTokenCount(overlapText);
                currentSentences = getOverlapSentences(currentSentences, overlap);
            }
            
            currentChunk.append(sentence).append(" ");
            currentSentences.add(sentence);
            currentLength += sentenceLength;
        }
        
        // 处理最后一个块
        if (currentLength > 0) {
            DocumentChunk chunk = createChunk(currentChunk.toString(), currentSentences, chunks.size());
            chunks.add(chunk);
        }
        
        return chunks;
    }
    
    private DocumentChunk createChunk(String content, List<String> sentences, int index) {
        return DocumentChunk.builder()
            .content(content.trim())
            .chunkIndex(index)
            .sentences(new ArrayList<>(sentences))
            .tokenCount(getTokenCount(content))
            .metadata(createChunkMetadata(sentences, index))
            .build();
    }
    
    private int getTokenCount(String text) {
        // 使用简单的token计算，实际可以使用更精确的tokenizer
        return text.split("\\s+").length;
    }
}
```

#### 1.3 语义边界分块策略
```java
@Component
public class SemanticChunkingStrategy implements ChunkingStrategy {
    
    @Autowired
    private SentenceTransformerService sentenceTransformer;
    
    @Override
    public List<DocumentChunk> chunk(DocumentContent content, ChunkingConfig config) {
        List<String> sentences = extractSentences(content);
        
        // 计算句子向量
        List<float[]> sentenceVectors = sentences.stream()
            .map(sentenceTransformer::encode)
            .collect(Collectors.toList());
        
        // 计算语义相似度
        List<Double> similarities = calculateSimilarities(sentenceVectors);
        
        // 识别语义边界
        List<Integer> boundaries = identifySemanticBoundaries(similarities, config.getSimilarityThreshold());
        
        // 创建语义块
        return createSemanticChunks(sentences, boundaries, config);
    }
    
    private List<Double> calculateSimilarities(List<float[]> vectors) {
        List<Double> similarities = new ArrayList<>();
        
        for (int i = 0; i < vectors.size() - 1; i++) {
            double similarity = cosineSimilarity(vectors.get(i), vectors.get(i + 1));
            similarities.add(similarity);
        }
        
        return similarities;
    }
    
    private List<Integer> identifySemanticBoundaries(List<Double> similarities, double threshold) {
        List<Integer> boundaries = new ArrayList<>();
        boundaries.add(0); // 起始边界
        
        for (int i = 0; i < similarities.size(); i++) {
            if (similarities.get(i) < threshold) {
                boundaries.add(i + 1);
            }
        }
        
        boundaries.add(similarities.size() + 1); // 结束边界
        return boundaries;
    }
    
    private List<DocumentChunk> createSemanticChunks(List<String> sentences, 
                                                   List<Integer> boundaries, 
                                                   ChunkingConfig config) {
        List<DocumentChunk> chunks = new ArrayList<>();
        
        for (int i = 0; i < boundaries.size() - 1; i++) {
            int start = boundaries.get(i);
            int end = boundaries.get(i + 1);
            
            List<String> chunkSentences = sentences.subList(start, end);
            String content = String.join(" ", chunkSentences);
            
            // 检查块大小限制
            if (getTokenCount(content) > config.getMaxChunkSize()) {
                // 递归分割大块
                List<DocumentChunk> subChunks = splitLargeChunk(chunkSentences, config);
                chunks.addAll(subChunks);
            } else {
                DocumentChunk chunk = DocumentChunk.builder()
                    .content(content)
                    .chunkIndex(chunks.size())
                    .sentences(chunkSentences)
                    .tokenCount(getTokenCount(content))
                    .chunkingMethod(ChunkingType.SEMANTIC_BOUNDARY)
                    .build();
                chunks.add(chunk);
            }
        }
        
        return chunks;
    }
}
```

#### 1.4 结构化分块策略
```java
@Component
public class StructuralChunkingStrategy implements ChunkingStrategy {
    
    @Override
    public List<DocumentChunk> chunk(DocumentContent content, ChunkingConfig config) {
        DocumentStructure structure = content.getStructure();
        List<DocumentChunk> chunks = new ArrayList<>();
        
        // 按章节分块
        for (Section section : structure.getSections()) {
            List<DocumentChunk> sectionChunks = chunkSection(section, config);
            chunks.addAll(sectionChunks);
        }
        
        // 处理表格
        for (Table table : structure.getTables()) {
            DocumentChunk tableChunk = chunkTable(table, chunks.size());
            chunks.add(tableChunk);
        }
        
        // 处理列表
        for (ListStructure list : structure.getLists()) {
            DocumentChunk listChunk = chunkList(list, chunks.size());
            chunks.add(listChunk);
        }
        
        return chunks;
    }
    
    private List<DocumentChunk> chunkSection(Section section, ChunkingConfig config) {
        List<DocumentChunk> chunks = new ArrayList<>();
        
        // 标题作为上下文
        String sectionTitle = section.getHeading().getText();
        
        // 分块段落内容
        StringBuilder currentChunk = new StringBuilder();
        currentChunk.append("# ").append(sectionTitle).append("\n\n");
        
        int currentLength = getTokenCount(currentChunk.toString());
        
        for (Paragraph paragraph : section.getParagraphs()) {
            String paragraphText = paragraph.getText();
            int paragraphLength = getTokenCount(paragraphText);
            
            if (currentLength + paragraphLength > config.getChunkSize() && currentLength > 0) {
                // 创建当前块
                DocumentChunk chunk = DocumentChunk.builder()
                    .content(currentChunk.toString().trim())
                    .chunkIndex(chunks.size())
                    .sectionTitle(sectionTitle)
                    .tokenCount(currentLength)
                    .chunkingMethod(ChunkingType.STRUCTURAL)
                    .build();
                chunks.add(chunk);
                
                // 开始新块，保留标题上下文
                currentChunk = new StringBuilder();
                currentChunk.append("# ").append(sectionTitle).append("\n\n");
                currentLength = getTokenCount(currentChunk.toString());
            }
            
            currentChunk.append(paragraphText).append("\n\n");
            currentLength += paragraphLength;
        }
        
        // 处理最后一个块
        if (currentLength > getTokenCount("# " + sectionTitle + "\n\n")) {
            DocumentChunk chunk = DocumentChunk.builder()
                .content(currentChunk.toString().trim())
                .chunkIndex(chunks.size())
                .sectionTitle(sectionTitle)
                .tokenCount(currentLength)
                .chunkingMethod(ChunkingType.STRUCTURAL)
                .build();
            chunks.add(chunk);
        }
        
        return chunks;
    }
    
    private DocumentChunk chunkTable(Table table, int chunkIndex) {
        StringBuilder tableContent = new StringBuilder();
        
        // 表格标题
        if (table.getCaption() != null) {
            tableContent.append("表格: ").append(table.getCaption()).append("\n\n");
        }
        
        // 表格内容
        List<List<String>> rows = table.getRows();
        if (!rows.isEmpty()) {
            // 表头
            List<String> headers = rows.get(0);
            tableContent.append("| ").append(String.join(" | ", headers)).append(" |\n");
            tableContent.append("|").append(" --- |".repeat(headers.size())).append("\n");
            
            // 数据行
            for (int i = 1; i < rows.size(); i++) {
                List<String> row = rows.get(i);
                tableContent.append("| ").append(String.join(" | ", row)).append(" |\n");
            }
        }
        
        return DocumentChunk.builder()
            .content(tableContent.toString())
            .chunkIndex(chunkIndex)
            .contentType(ContentType.TABLE)
            .tokenCount(getTokenCount(tableContent.toString()))
            .chunkingMethod(ChunkingType.STRUCTURAL)
            .build();
    }
}
```

### 2. 自适应分块算法

#### 2.1 动态分块策略
```java
@Component
public class AdaptiveChunkingStrategy implements ChunkingStrategy {
    
    @Autowired
    private List<ChunkingStrategy> strategies;
    
    @Override
    public List<DocumentChunk> chunk(DocumentContent content, ChunkingConfig config) {
        // 分析文档特征
        DocumentCharacteristics characteristics = analyzeDocument(content);
        
        // 选择最优策略
        ChunkingStrategy optimalStrategy = selectOptimalStrategy(characteristics, config);
        
        // 执行分块
        List<DocumentChunk> chunks = optimalStrategy.chunk(content, config);
        
        // 分块优化
        return optimizeChunks(chunks, characteristics, config);
    }
    
    private DocumentCharacteristics analyzeDocument(DocumentContent content) {
        return DocumentCharacteristics.builder()
            .documentLength(calculateDocumentLength(content))
            .structureComplexity(calculateStructureComplexity(content))
            .contentDensity(calculateContentDensity(content))
            .languageComplexity(calculateLanguageComplexity(content))
            .domainSpecificity(calculateDomainSpecificity(content))
            .build();
    }
    
    private ChunkingStrategy selectOptimalStrategy(DocumentCharacteristics characteristics, 
                                                 ChunkingConfig config) {
        // 基于文档特征选择策略
        if (characteristics.getStructureComplexity() > 0.8) {
            return getStrategy(ChunkingType.STRUCTURAL);
        } else if (characteristics.getLanguageComplexity() > 0.7) {
            return getStrategy(ChunkingType.SEMANTIC_BOUNDARY);
        } else {
            return getStrategy(ChunkingType.FIXED_SIZE);
        }
    }
    
    private List<DocumentChunk> optimizeChunks(List<DocumentChunk> chunks, 
                                             DocumentCharacteristics characteristics,
                                             ChunkingConfig config) {
        List<DocumentChunk> optimizedChunks = new ArrayList<>();
        
        for (DocumentChunk chunk : chunks) {
            // 检查块质量
            ChunkQuality quality = evaluateChunkQuality(chunk, characteristics);
            
            if (quality.getScore() < config.getQualityThreshold()) {
                // 重新分块
                List<DocumentChunk> reChunked = rechunkLowQualityChunk(chunk, config);
                optimizedChunks.addAll(reChunked);
            } else {
                optimizedChunks.add(chunk);
            }
        }
        
        return optimizedChunks;
    }
}
```

#### 2.2 上下文窗口优化
```java
@Component
public class ContextWindowOptimizer {
    
    public List<DocumentChunk> optimizeContextWindows(List<DocumentChunk> chunks, 
                                                    ChunkingConfig config) {
        List<DocumentChunk> optimizedChunks = new ArrayList<>();
        
        for (int i = 0; i < chunks.size(); i++) {
            DocumentChunk chunk = chunks.get(i);
            
            // 计算最优重叠
            int optimalOverlap = calculateOptimalOverlap(chunk, chunks, i, config);
            
            // 调整块边界
            DocumentChunk adjustedChunk = adjustChunkBoundaries(chunk, chunks, i, optimalOverlap);
            
            optimizedChunks.add(adjustedChunk);
        }
        
        return optimizedChunks;
    }
    
    private int calculateOptimalOverlap(DocumentChunk chunk, List<DocumentChunk> allChunks, 
                                      int index, ChunkingConfig config) {
        // 基于内容复杂度调整重叠
        double contentComplexity = calculateContentComplexity(chunk);
        
        int baseOverlap = config.getOverlap();
        
        if (contentComplexity > 0.8) {
            return (int) (baseOverlap * 1.5); // 复杂内容增加重叠
        } else if (contentComplexity < 0.3) {
            return (int) (baseOverlap * 0.7); // 简单内容减少重叠
        }
        
        return baseOverlap;
    }
    
    private DocumentChunk adjustChunkBoundaries(DocumentChunk chunk, List<DocumentChunk> allChunks, 
                                              int index, int overlap) {
        String content = chunk.getContent();
        
        // 向前扩展
        if (index > 0) {
            String previousContent = allChunks.get(index - 1).getContent();
            String forwardContext = extractContextFromPrevious(previousContent, overlap);
            content = forwardContext + "\n" + content;
        }
        
        // 向后扩展
        if (index < allChunks.size() - 1) {
            String nextContent = allChunks.get(index + 1).getContent();
            String backwardContext = extractContextFromNext(nextContent, overlap);
            content = content + "\n" + backwardContext;
        }
        
        return chunk.toBuilder()
            .content(content)
            .tokenCount(getTokenCount(content))
            .hasExtendedContext(true)
            .build();
    }
}
```

### 3. 特殊内容处理

#### 3.1 代码块分块处理
```java
@Component
public class CodeChunkingProcessor {
    
    public List<DocumentChunk> chunkCodeContent(String codeContent, String language) {
        List<DocumentChunk> chunks = new ArrayList<>();
        
        // 解析代码结构
        CodeStructure structure = parseCodeStructure(codeContent, language);
        
        // 按函数/类分块
        for (CodeBlock codeBlock : structure.getBlocks()) {
            DocumentChunk chunk = createCodeChunk(codeBlock);
            chunks.add(chunk);
        }
        
        return chunks;
    }
    
    private CodeStructure parseCodeStructure(String code, String language) {
        switch (language.toLowerCase()) {
            case "java":
                return parseJavaCode(code);
            case "python":
                return parsePythonCode(code);
            case "javascript":
                return parseJavaScriptCode(code);
            default:
                return parseGenericCode(code);
        }
    }
    
    private DocumentChunk createCodeChunk(CodeBlock codeBlock) {
        StringBuilder content = new StringBuilder();
        
        // 添加上下文信息
        if (codeBlock.getClassName() != null) {
            content.append("类: ").append(codeBlock.getClassName()).append("\n");
        }
        
        if (codeBlock.getFunctionName() != null) {
            content.append("函数: ").append(codeBlock.getFunctionName()).append("\n");
        }
        
        // 添加注释
        if (codeBlock.getDocstring() != null) {
            content.append("说明: ").append(codeBlock.getDocstring()).append("\n");
        }
        
        content.append("```").append(codeBlock.getLanguage()).append("\n");
        content.append(codeBlock.getCode());
        content.append("\n```");
        
        return DocumentChunk.builder()
            .content(content.toString())
            .contentType(ContentType.CODE)
            .language(codeBlock.getLanguage())
            .functionName(codeBlock.getFunctionName())
            .className(codeBlock.getClassName())
            .build();
    }
}
```

#### 3.2 表格分块处理
```java
@Component
public class TableChunkingProcessor {
    
    public List<DocumentChunk> chunkTable(Table table, ChunkingConfig config) {
        List<DocumentChunk> chunks = new ArrayList<>();
        
        if (table.getRows().size() <= config.getMaxTableRows()) {
            // 小表格整体作为一个块
            chunks.add(createSingleTableChunk(table));
        } else {
            // 大表格按行分块
            chunks.addAll(createMultipleTableChunks(table, config));
        }
        
        return chunks;
    }
    
    private DocumentChunk createSingleTableChunk(Table table) {
        StringBuilder content = new StringBuilder();
        
        // 表格标题和描述
        if (table.getCaption() != null) {
            content.append("表格标题: ").append(table.getCaption()).append("\n\n");
        }
        
        // 表格内容
        content.append(formatTableAsMarkdown(table));
        
        // 表格摘要
        content.append("\n\n表格摘要: ").append(generateTableSummary(table));
        
        return DocumentChunk.builder()
            .content(content.toString())
            .contentType(ContentType.TABLE)
            .tableMetadata(extractTableMetadata(table))
            .build();
    }
    
    private List<DocumentChunk> createMultipleTableChunks(Table table, ChunkingConfig config) {
        List<DocumentChunk> chunks = new ArrayList<>();
        List<List<String>> rows = table.getRows();
        List<String> headers = rows.get(0);
        
        int chunkSize = config.getMaxTableRows();
        
        for (int i = 1; i < rows.size(); i += chunkSize) {
            int endIndex = Math.min(i + chunkSize, rows.size());
            
            StringBuilder content = new StringBuilder();
            
            // 表格标题
            if (table.getCaption() != null) {
                content.append("表格: ").append(table.getCaption())
                       .append(" (第").append(i).append("-").append(endIndex-1).append("行)\n\n");
            }
            
            // 表头
            content.append("| ").append(String.join(" | ", headers)).append(" |\n");
            content.append("|").append(" --- |".repeat(headers.size())).append("\n");
            
            // 数据行
            for (int j = i; j < endIndex; j++) {
                List<String> row = rows.get(j);
                content.append("| ").append(String.join(" | ", row)).append(" |\n");
            }
            
            DocumentChunk chunk = DocumentChunk.builder()
                .content(content.toString())
                .contentType(ContentType.TABLE)
                .chunkIndex(chunks.size())
                .tableMetadata(extractPartialTableMetadata(table, i, endIndex))
                .build();
            
            chunks.add(chunk);
        }
        
        return chunks;
    }
}
```

## 配置与扩展

### 分块配置
```java
@ConfigurationProperties(prefix = "rag.chunking")
@Data
public class ChunkingConfig {
    
    // 基础配置
    private int chunkSize = 1000;
    private int overlap = 200;
    private int maxChunkSize = 2000;
    private int minChunkSize = 100;
    
    // 相似度控制
    private double similarityThreshold = 0.6;
    
    // 特殊内容配置
    private int maxTableRows = 50;
    private boolean preserveCodeStructure = true;
    private boolean enableSemanticBoundary = true;
    
    // 策略选择
    private ChunkingType defaultStrategy = ChunkingType.ADAPTIVE;
    private Map<DocumentType, ChunkingType> strategyMapping = new HashMap<>();
}
```

### 6. 实体感知分块策略

#### 6.1 实体感知分块策略实现
```java
@Component
public class EntityAwareChunkingStrategy implements ChunkingStrategy {

    @Autowired
    private EntityRecognitionService entityRecognitionService;

    @Autowired
    private RelationshipAnalysisService relationshipAnalysisService;

    @Override
    public List<DocumentChunk> chunk(DocumentContent content, ChunkingConfig config) {
        // 1. 识别文档中的所有实体
        List<RecognizedEntity> entities = recognizeEntities(content);

        // 2. 分析实体间的关系
        List<EntityRelationship> relationships = analyzeRelationships(content, entities);

        // 3. 构建实体关系图
        EntityRelationshipGraph graph = buildRelationshipGraph(entities, relationships);

        // 4. 基于实体边界进行分块
        List<DocumentChunk> chunks = performEntityAwareChunking(content, graph, config);

        // 5. 优化分块结果
        return optimizeChunks(chunks, graph, config);
    }

    private List<RecognizedEntity> recognizeEntities(DocumentContent content) {
        List<RecognizedEntity> allEntities = new ArrayList<>();

        for (ContentBlock block : content.getBlocks()) {
            List<RecognizedEntity> blockEntities = entityRecognitionService.recognize(
                block.getContent(),
                EntityRecognitionConfig.builder()
                    .confidenceThreshold(0.7)
                    .maxEntities(50)
                    .build()
            );

            // 添加位置信息
            for (RecognizedEntity entity : blockEntities) {
                entity.setBlockId(block.getId());
                entity.setStartPosition(block.getStartPosition() + entity.getStartOffset());
                entity.setEndPosition(block.getStartPosition() + entity.getEndOffset());
            }

            allEntities.addAll(blockEntities);
        }

        return allEntities;
    }

    private List<EntityRelationship> analyzeRelationships(DocumentContent content, List<RecognizedEntity> entities) {
        List<EntityRelationship> relationships = new ArrayList<>();

        // 分析同一段落内的实体关系
        for (ContentBlock block : content.getBlocks()) {
            List<RecognizedEntity> blockEntities = entities.stream()
                .filter(e -> e.getBlockId().equals(block.getId()))
                .collect(Collectors.toList());

            if (blockEntities.size() >= 2) {
                List<EntityRelationship> blockRelationships = relationshipAnalysisService.analyze(
                    block.getContent(), blockEntities
                );
                relationships.addAll(blockRelationships);
            }
        }

        // 分析跨段落的实体关系
        relationships.addAll(analyzeCrossParagraphRelationships(content, entities));

        return relationships;
    }

    private EntityRelationshipGraph buildRelationshipGraph(List<RecognizedEntity> entities,
                                                          List<EntityRelationship> relationships) {
        EntityRelationshipGraph graph = new EntityRelationshipGraph();

        // 添加实体节点
        for (RecognizedEntity entity : entities) {
            graph.addEntity(entity);
        }

        // 添加关系边
        for (EntityRelationship relationship : relationships) {
            graph.addRelationship(relationship);
        }

        return graph;
    }

    private List<DocumentChunk> performEntityAwareChunking(DocumentContent content,
                                                         EntityRelationshipGraph graph,
                                                         ChunkingConfig config) {
        List<DocumentChunk> chunks = new ArrayList<>();

        // 获取实体边界点
        List<Integer> entityBoundaries = getEntityBoundaries(graph.getEntities());

        // 获取句子边界点
        List<Integer> sentenceBoundaries = getSentenceBoundaries(content);

        // 合并边界点并排序
        Set<Integer> allBoundaries = new TreeSet<>();
        allBoundaries.addAll(entityBoundaries);
        allBoundaries.addAll(sentenceBoundaries);

        List<Integer> sortedBoundaries = new ArrayList<>(allBoundaries);

        // 基于边界点进行分块
        int chunkStart = 0;
        StringBuilder currentChunk = new StringBuilder();
        List<RecognizedEntity> currentEntities = new ArrayList<>();

        for (int i = 0; i < sortedBoundaries.size(); i++) {
            int boundary = sortedBoundaries.get(i);

            // 添加当前段落到分块
            String segment = extractTextSegment(content, chunkStart, boundary);
            currentChunk.append(segment);

            // 添加该段落中的实体
            List<RecognizedEntity> segmentEntities = getEntitiesInRange(graph.getEntities(), chunkStart, boundary);
            currentEntities.addAll(segmentEntities);

            // 检查是否应该结束当前分块
            if (shouldEndChunk(currentChunk.toString(), currentEntities, graph, config)) {
                // 创建分块
                DocumentChunk chunk = createChunk(currentChunk.toString(), currentEntities, chunks.size());
                chunks.add(chunk);

                // 重置状态
                currentChunk = new StringBuilder();
                currentEntities = new ArrayList<>();
                chunkStart = boundary;
            }
        }

        // 处理最后一个分块
        if (currentChunk.length() > 0) {
            DocumentChunk chunk = createChunk(currentChunk.toString(), currentEntities, chunks.size());
            chunks.add(chunk);
        }

        return chunks;
    }

    private boolean shouldEndChunk(String currentText, List<RecognizedEntity> currentEntities,
                                 EntityRelationshipGraph graph, ChunkingConfig config) {
        // 检查长度限制
        if (getTokenCount(currentText) >= config.getChunkSize()) {
            return true;
        }

        // 检查实体完整性
        if (!isEntitySetComplete(currentEntities, graph)) {
            return false;
        }

        // 检查关系完整性
        if (!isRelationshipSetComplete(currentEntities, graph)) {
            return false;
        }

        return true;
    }

    private boolean isEntitySetComplete(List<RecognizedEntity> entities, EntityRelationshipGraph graph) {
        // 检查是否有实体被截断
        for (RecognizedEntity entity : entities) {
            if (entity.getEndPosition() > getMaxPosition(entities)) {
                return false;
            }
        }
        return true;
    }

    private boolean isRelationshipSetComplete(List<RecognizedEntity> entities, EntityRelationshipGraph graph) {
        // 检查实体间的关系是否完整
        for (RecognizedEntity entity : entities) {
            List<EntityRelationship> relationships = graph.getRelationships(entity);
            for (EntityRelationship relationship : relationships) {
                RecognizedEntity relatedEntity = relationship.getTargetEntity();
                if (!entities.contains(relatedEntity)) {
                    // 如果相关实体不在当前分块中，检查关系的重要性
                    if (relationship.getImportance() > 0.7) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    @Override
    public String getStrategyName() {
        return "EntityAwareChunking";
    }

    @Override
    public ChunkingType getType() {
        return ChunkingType.ENTITY_AWARE;
    }

    @Override
    public boolean supports(DocumentType documentType) {
        return true; // 支持所有文档类型
    }
}
```

#### 6.2 实体关系图数据结构
```java
public class EntityRelationshipGraph {
    private Map<String, RecognizedEntity> entities = new HashMap<>();
    private Map<String, List<EntityRelationship>> relationships = new HashMap<>();

    public void addEntity(RecognizedEntity entity) {
        entities.put(entity.getId(), entity);
        relationships.putIfAbsent(entity.getId(), new ArrayList<>());
    }

    public void addRelationship(EntityRelationship relationship) {
        String sourceId = relationship.getSourceEntity().getId();
        relationships.computeIfAbsent(sourceId, k -> new ArrayList<>()).add(relationship);
    }

    public List<RecognizedEntity> getEntities() {
        return new ArrayList<>(entities.values());
    }

    public List<EntityRelationship> getRelationships(RecognizedEntity entity) {
        return relationships.getOrDefault(entity.getId(), Collections.emptyList());
    }

    public List<RecognizedEntity> getRelatedEntities(RecognizedEntity entity, int maxHops) {
        Set<RecognizedEntity> visited = new HashSet<>();
        Queue<Pair<RecognizedEntity, Integer>> queue = new LinkedList<>();

        queue.offer(Pair.of(entity, 0));
        visited.add(entity);

        List<RecognizedEntity> relatedEntities = new ArrayList<>();

        while (!queue.isEmpty()) {
            Pair<RecognizedEntity, Integer> current = queue.poll();
            RecognizedEntity currentEntity = current.getLeft();
            int hops = current.getRight();

            if (hops > 0) {
                relatedEntities.add(currentEntity);
            }

            if (hops < maxHops) {
                List<EntityRelationship> entityRelationships = getRelationships(currentEntity);
                for (EntityRelationship rel : entityRelationships) {
                    RecognizedEntity target = rel.getTargetEntity();
                    if (!visited.contains(target)) {
                        visited.add(target);
                        queue.offer(Pair.of(target, hops + 1));
                    }
                }
            }
        }

        return relatedEntities;
    }
}
```



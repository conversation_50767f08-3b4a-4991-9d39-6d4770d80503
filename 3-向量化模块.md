# 向量化模块 (Vectorization)

## 方案说明

### 1. 技术方案概述
向量化模块负责将文本块转换为稠密向量和稀疏向量表示，是RAG系统语义理解和混合检索的核心基础，直接影响检索质量和系统性能。该模块采用混合向量化、多模型融合、批量优化和智能缓存的设计理念，确保生成高质量的稠密和稀疏向量表示。

### 2. 核心技术特点
- **混合向量化**：同时生成稠密向量和稀疏向量，支持混合检索
- **多模型支持**：集成阿里云、OpenAI、SPLADE、BGE-M3等多种向量化服务
- **稀疏向量优化**：支持SPLADE、BGE-M3等先进稀疏向量模型
- **批量优化**：支持批量向量化处理，提高处理效率
- **异常检测**：智能检测和处理异常向量
- **缓存机制**：智能缓存策略，避免重复计算
- **负载均衡**：多服务负载均衡，确保高可用性
- **实体向量化**：支持实体和关系的专门向量化，增强知识图谱集成

### 3. 技术架构优势
- **高性能**：异步批量处理，支持大规模文本向量化
- **高可靠**：多重容错机制和服务降级策略
- **可扩展**：支持新模型快速接入和动态切换
- **智能化**：自动模型选择和参数优化

## 流程图

```mermaid
graph TD
    A[文本分块输入] --> B[文本预处理]
    B --> C[模型选择策略]

    C --> D{向量化策略选择}
    D -->|稠密向量| E[稠密向量化分支]
    D -->|稀疏向量| F[稀疏向量化分支]
    D -->|混合向量| G[混合向量化分支]
    D -->|实体向量| H[实体向量化分支]

    E --> E1[阿里云稠密向量服务]
    E --> E2[OpenAI稠密向量服务]
    E --> E3[本地稠密向量服务]

    F --> F1[SPLADE稀疏向量服务]
    F --> F2[BGE-M3稀疏向量服务]
    F --> F3[自定义稀疏向量服务]

    G --> G1[并行稠密+稀疏向量化]

    H --> H1[实体向量化服务]
    H --> H2[关系向量化服务]
    H --> H3[知识图谱增强向量化]

    B --> I[批量处理优化]
    I --> J[缓存检查]
    J --> K{缓存命中?}
    K -->|命中| L[缓存向量获取]
    K -->|未命中| M[向量化计算]

    E1 --> M
    E2 --> M
    E3 --> M
    F1 --> M
    F2 --> M
    F3 --> M
    G1 --> M
    H1 --> M
    H2 --> M
    H3 --> M

    M --> N[向量标准化]
    N --> O[向量缓存存储]
    L --> P[向量后处理]
    O --> P

    P --> Q[维度验证]
    Q --> R[异常检测]
    R --> S[稀疏向量优化]
    S --> T[实体向量关联]
    T --> U[元数据关联]

    U --> V[混合向量索引构建]
    V --> W[批量输出]

    W --> X[检索模块]

    style A fill:#e1f5fe
    style U fill:#c8e6c9
    style V fill:#fff3e0
```

## 流程步骤说明

### 阶段一：文本预处理与模型选择
1. **文本预处理**：
   - 文本清洗：移除特殊字符、标准化编码
   - 长度检查：验证文本长度是否符合模型要求
   - 语言检测：识别文本语言，选择合适的模型
   - 格式标准化：统一文本格式和编码

2. **模型选择策略**：
   - **性能优先**：选择速度最快的可用模型
   - **质量优先**：选择效果最好的模型
   - **成本优先**：选择成本最低的模型
   - **负载均衡**：根据当前负载分配模型

### 阶段二：向量化策略选择与执行
#### 稠密向量化分支
1. **稠密向量模型选择**：
   - **阿里云文本向量化**：高质量中文语义理解
   - **OpenAI Embeddings**：强大的多语言支持
   - **本地模型**：BGE、M3E等开源模型
   - **模型融合**：多模型结果融合

2. **稠密向量生成**：
   - 文本预处理和标准化
   - 模型推理生成向量
   - 向量标准化和归一化
   - 维度验证和异常检测

#### 稀疏向量化分支
1. **稀疏向量模型选择**：
   - **SPLADE模型**：基于BERT的稀疏向量生成
   - **BGE-M3模型**：多语言稀疏向量支持
   - **自定义模型**：领域特定的稀疏向量模型
   - **TF-IDF增强**：传统方法与深度学习结合

2. **稀疏向量生成**：
   - 词汇表构建和token映射
   - 稀疏权重计算和优化
   - 低权重过滤和压缩
   - 稀疏向量格式转换

#### 混合向量化分支
1. **并行向量化**：
   - 同时生成稠密向量和稀疏向量
   - 向量对齐和一致性检查
   - 混合向量封装和存储
   - 性能优化和资源管理

#### 实体向量化分支
1. **实体向量化**：
   - 实体文本的专门向量化处理
   - 实体类型感知的向量生成
   - 实体上下文信息的融合
   - 实体向量的标准化和优化

2. **关系向量化**：
   - 关系文本的向量表示
   - 关系类型的向量编码
   - 关系强度的向量权重
   - 关系向量的归一化处理

3. **知识图谱增强向量化**：
   - 基于知识图谱的上下文增强
   - 实体关系信息的向量融合
   - 图谱结构信息的向量编码
   - 知识驱动的向量优化

### 阶段三：批量处理优化
1. **批量分组**：
   - 按照向量类型对文本进行分组
   - 根据文本长度进行批次划分
   - 考虑API限制进行批量大小调整
   - 稠密和稀疏向量并行处理

2. **缓存策略**：
   - **分层缓存**：稠密向量和稀疏向量分别缓存
   - **内存缓存**：热点文本的向量缓存
   - **持久化缓存**：Redis缓存常用向量
   - **缓存更新**：定期更新和清理缓存
   - **缓存命中率优化**：智能缓存策略

### 阶段四：多模型混合向量化处理
#### 稠密向量化服务
1. **阿里云稠密向量服务**：
   - 调用阿里云文本向量化API
   - 支持中文优化的语义理解
   - 处理API限流和错误重试
   - 1024维稠密向量输出

2. **OpenAI稠密向量服务**：
   - 使用text-embedding-3-large等模型
   - 支持多语言和长文本处理
   - 批量处理优化
   - 可配置向量维度

3. **本地稠密向量服务**：
   - BGE、M3E等开源模型
   - GPU加速推理
   - 自定义模型微调支持
   - 离线部署和隐私保护

#### 稀疏向量化服务
1. **SPLADE向量服务**：
   - 基于BERT的稀疏向量生成
   - 保留关键词匹配能力
   - 可解释的稀疏权重
   - 支持多语言扩展

2. **BGE-M3稀疏向量服务**：
   - 多语言稀疏向量支持
   - 统一的稠密+稀疏向量框架
   - 高效的稀疏向量压缩
   - 与稠密向量的协同优化

3. **自定义稀疏向量服务**：
   - TF-IDF增强的稀疏向量
   - 领域特定词汇表优化
   - 动态权重调整
   - 与业务场景深度结合

#### 混合向量融合服务
1. **并行向量化**：
   - 同时生成稠密向量和稀疏向量
   - 资源调度和负载均衡
   - 异步处理和结果聚合
   - 性能监控和优化

2. **向量对齐**：
   - 确保稠密和稀疏向量的一致性
   - 文本预处理的统一标准
   - 向量质量交叉验证
   - 异常向量检测和处理

3. **混合向量封装**：
   - 统一的混合向量数据结构
   - 元数据关联和版本管理
   - 存储格式优化
   - 检索接口适配

### 阶段五：混合向量后处理与输出
1. **向量标准化**：
   - **稠密向量标准化**：L2标准化为单位向量
   - **稀疏向量优化**：权重归一化和稀疏度控制
   - **维度对齐**：确保向量维度一致性
   - **精度控制**：控制向量的数值精度

2. **稀疏向量优化**：
   - **权重过滤**：移除低权重维度（< 0.01）
   - **稀疏度控制**：保持合适的稀疏度比例
   - **Top-K选择**：保留最重要的K个维度
   - **压缩存储**：优化稀疏向量存储格式

3. **异常检测**：
   - **零向量检测**：识别和处理零向量
   - **异常值检测**：发现和处理异常向量值
   - **稀疏向量验证**：检查稀疏向量的合理性
   - **一致性检查**：验证稠密和稀疏向量的一致性

4. **元数据关联**：
   - 关联原始文本信息
   - 记录稠密和稀疏向量化模型信息
   - 添加向量统计信息和时间戳
   - 构建混合向量到文档的映射关系

5. **混合向量索引构建**：
   - 为混合向量分配唯一标识符
   - 构建稠密向量索引结构（HNSW）
   - 构建稀疏向量索引结构（倒排索引）
   - 准备Milvus混合向量存储格式

6. **批量输出**：
   - 封装为标准化混合向量对象
   - 生成向量化报告和统计信息
   - 输出稠密和稀疏向量的分布分析
   - 为检索模块准备混合向量输入数据

## 模块概述

向量化模块负责将文本块转换为高维向量表示，是RAG系统语义理解和检索的核心基础，直接影响检索质量和系统性能。

## 核心功能架构

### 1. 多模型向量化引擎

#### 1.1 混合向量化服务接口设计
```java
// 稠密向量服务接口
public interface DenseEmbeddingService {
    float[] generateDenseEmbedding(String text, DenseEmbeddingConfig config);
    List<float[]> batchGenerateDenseEmbedding(List<String> texts, DenseEmbeddingConfig config);
    int getDimension();
    String getModelName();
    boolean supportsLanguage(String language);
}

// 稀疏向量服务接口
public interface SparseEmbeddingService {
    SparseVector generateSparseEmbedding(String text, SparseEmbeddingConfig config);
    List<SparseVector> batchGenerateSparseEmbedding(List<String> texts, SparseEmbeddingConfig config);
    int getVocabSize();
    String getModelName();
    boolean supportsLanguage(String language);
}

// 混合向量服务接口
public interface HybridEmbeddingService {
    HybridVector generateHybridEmbedding(String text, HybridEmbeddingConfig config);
    List<HybridVector> batchGenerateHybridEmbedding(List<String> texts, HybridEmbeddingConfig config);
    boolean supportsDenseVector();
    boolean supportsSparseVector();
}

@Component
public class HybridEmbeddingServiceImpl implements HybridEmbeddingService {

    @Autowired
    private DenseEmbeddingService denseEmbeddingService;

    @Autowired
    private SparseEmbeddingService sparseEmbeddingService;

    @Autowired
    private VectorCacheService vectorCacheService;

    @Override
    public HybridVector generateHybridEmbedding(String text, HybridEmbeddingConfig config) {
        // 检查缓存
        String cacheKey = generateCacheKey(text, config);
        HybridVector cachedVector = vectorCacheService.getHybridVector(cacheKey);
        if (cachedVector != null) {
            return cachedVector;
        }

        // 并行生成稠密向量和稀疏向量
        CompletableFuture<float[]> denseVectorFuture = CompletableFuture.supplyAsync(() ->
            denseEmbeddingService.generateDenseEmbedding(text, config.getDenseConfig()));

        CompletableFuture<SparseVector> sparseVectorFuture = CompletableFuture.supplyAsync(() ->
            sparseEmbeddingService.generateSparseEmbedding(text, config.getSparseConfig()));

        try {
            // 等待两个向量生成完成
            float[] denseVector = denseVectorFuture.get();
            SparseVector sparseVector = sparseVectorFuture.get();

            // 构建混合向量
            HybridVector hybridVector = HybridVector.builder()
                .text(text)
                .denseVector(denseVector)
                .sparseVector(sparseVector)
                .denseModel(config.getDenseConfig().getModelName())
                .sparseModel(config.getSparseConfig().getModelName())
                .timestamp(System.currentTimeMillis())
                .build();

            // 缓存结果
            vectorCacheService.putHybridVector(cacheKey, hybridVector);

            return hybridVector;

        } catch (Exception e) {
            throw new VectorGenerationException("Failed to generate hybrid vector", e);
        }
    }

    @Override
    public List<HybridVector> batchGenerateHybridEmbedding(List<String> texts,
                                                         HybridEmbeddingConfig config) {
        // 批量处理优化
        List<String> uncachedTexts = new ArrayList<>();
        Map<String, HybridVector> cachedVectors = new HashMap<>();

        // 检查缓存
        for (String text : texts) {
            String cacheKey = generateCacheKey(text, config);
            HybridVector cachedVector = vectorCacheService.getHybridVector(cacheKey);
            if (cachedVector != null) {
                cachedVectors.put(text, cachedVector);
            } else {
                uncachedTexts.add(text);
            }
        }

        // 批量生成未缓存的向量
        List<HybridVector> newVectors = new ArrayList<>();
        if (!uncachedTexts.isEmpty()) {
            // 并行批量生成
            CompletableFuture<List<float[]>> denseVectorsFuture = CompletableFuture.supplyAsync(() ->
                denseEmbeddingService.batchGenerateDenseEmbedding(uncachedTexts, config.getDenseConfig()));

            CompletableFuture<List<SparseVector>> sparseVectorsFuture = CompletableFuture.supplyAsync(() ->
                sparseEmbeddingService.batchGenerateSparseEmbedding(uncachedTexts, config.getSparseConfig()));

            try {
                List<float[]> denseVectors = denseVectorsFuture.get();
                List<SparseVector> sparseVectors = sparseVectorsFuture.get();

                for (int i = 0; i < uncachedTexts.size(); i++) {
                    String text = uncachedTexts.get(i);
                    HybridVector hybridVector = HybridVector.builder()
                        .text(text)
                        .denseVector(denseVectors.get(i))
                        .sparseVector(sparseVectors.get(i))
                        .denseModel(config.getDenseConfig().getModelName())
                        .sparseModel(config.getSparseConfig().getModelName())
                        .timestamp(System.currentTimeMillis())
                        .build();

                    newVectors.add(hybridVector);

                    // 缓存新生成的向量
                    String cacheKey = generateCacheKey(text, config);
                    vectorCacheService.putHybridVector(cacheKey, hybridVector);
                }

            } catch (Exception e) {
                throw new VectorGenerationException("Failed to batch generate hybrid vectors", e);
            }
        }

        // 合并结果
        List<HybridVector> result = new ArrayList<>();
        for (String text : texts) {
            HybridVector vector = cachedVectors.get(text);
            if (vector == null) {
                vector = newVectors.stream()
                    .filter(v -> v.getText().equals(text))
                    .findFirst()
                    .orElseThrow(() -> new VectorGenerationException("Vector not found for text: " + text));
            }
            result.add(vector);
        }

        return result;
    }

    private String generateCacheKey(String text, HybridEmbeddingConfig config) {
        return String.format("hybrid:%s:%s:%s",
            config.getDenseConfig().getModelName(),
            config.getSparseConfig().getModelName(),
            DigestUtils.md5Hex(text));
    }
}
```

#### 1.2 SPLADE稀疏向量服务实现
```java
@Component
public class SpladeEmbeddingService implements SparseEmbeddingService {

    @Autowired
    private SpladeModel spladeModel;

    @Autowired
    private TokenizerService tokenizerService;

    @Override
    public SparseVector generateSparseEmbedding(String text, SparseEmbeddingConfig config) {
        // 文本预处理
        String processedText = preprocessText(text);

        // 使用SPLADE模型生成稀疏向量
        Map<String, Float> tokenWeights = spladeModel.encode(processedText);

        // 转换为稀疏向量格式
        List<Integer> indices = new ArrayList<>();
        List<Float> values = new ArrayList<>();

        for (Map.Entry<String, Float> entry : tokenWeights.entrySet()) {
            float weight = entry.getValue();
            if (weight > config.getMinWeight()) {
                int tokenId = tokenizerService.getTokenId(entry.getKey());
                if (tokenId >= 0) {
                    indices.add(tokenId);
                    values.add(weight);
                }
            }
        }

        // 按权重排序并保留Top-K
        if (config.getTopK() > 0 && indices.size() > config.getTopK()) {
            List<Pair<Integer, Float>> pairs = new ArrayList<>();
            for (int i = 0; i < indices.size(); i++) {
                pairs.add(Pair.of(indices.get(i), values.get(i)));
            }

            pairs.sort((a, b) -> Float.compare(b.getRight(), a.getRight()));
            pairs = pairs.subList(0, config.getTopK());

            indices.clear();
            values.clear();
            for (Pair<Integer, Float> pair : pairs) {
                indices.add(pair.getLeft());
                values.add(pair.getRight());
            }
        }

        return SparseVector.builder()
            .indices(indices)
            .values(values)
            .dimension(tokenizerService.getVocabSize())
            .sparsity(calculateSparsity(indices.size(), tokenizerService.getVocabSize()))
            .build();
    }

    @Override
    public List<SparseVector> batchGenerateSparseEmbedding(List<String> texts,
                                                         SparseEmbeddingConfig config) {
        // 批量预处理
        List<String> processedTexts = texts.stream()
            .map(this::preprocessText)
            .collect(Collectors.toList());

        // 批量编码
        List<Map<String, Float>> batchResults = spladeModel.batchEncode(processedTexts);

        // 转换为稀疏向量
        List<SparseVector> sparseVectors = new ArrayList<>();
        for (Map<String, Float> tokenWeights : batchResults) {
            SparseVector sparseVector = convertToSparseVector(tokenWeights, config);
            sparseVectors.add(sparseVector);
        }

        return sparseVectors;
    }

    private String preprocessText(String text) {
        // 文本清洗和标准化
        return text.toLowerCase()
            .replaceAll("[^\\p{L}\\p{N}\\s]", " ")
            .replaceAll("\\s+", " ")
            .trim();
    }

    private SparseVector convertToSparseVector(Map<String, Float> tokenWeights,
                                             SparseEmbeddingConfig config) {
        List<Integer> indices = new ArrayList<>();
        List<Float> values = new ArrayList<>();

        for (Map.Entry<String, Float> entry : tokenWeights.entrySet()) {
            float weight = entry.getValue();
            if (weight > config.getMinWeight()) {
                int tokenId = tokenizerService.getTokenId(entry.getKey());
                if (tokenId >= 0) {
                    indices.add(tokenId);
                    values.add(weight);
                }
            }
        }

        return SparseVector.builder()
            .indices(indices)
            .values(values)
            .dimension(tokenizerService.getVocabSize())
            .sparsity(calculateSparsity(indices.size(), tokenizerService.getVocabSize()))
            .build();
    }

    private double calculateSparsity(int nonZeroCount, int totalDimension) {
        return 1.0 - (double) nonZeroCount / totalDimension;
    }

    @Override
    public int getVocabSize() {
        return tokenizerService.getVocabSize();
    }

    @Override
    public String getModelName() {
        return "SPLADE";
    }

    @Override
    public boolean supportsLanguage(String language) {
        return Arrays.asList("en", "zh", "multilingual").contains(language);
    }
}
```

#### 1.3 BGE-M3稀疏向量服务实现
```java
@Component
public class BgeM3EmbeddingService implements SparseEmbeddingService, DenseEmbeddingService {

    @Autowired
    private BgeM3Model bgeM3Model;

    @Override
    public SparseVector generateSparseEmbedding(String text, SparseEmbeddingConfig config) {
        // 使用BGE-M3生成稀疏向量
        BgeM3Output output = bgeM3Model.encode(text);
        Map<Integer, Float> sparseWeights = output.getSparseVector();

        // 过滤和优化
        List<Integer> indices = new ArrayList<>();
        List<Float> values = new ArrayList<>();

        for (Map.Entry<Integer, Float> entry : sparseWeights.entrySet()) {
            if (entry.getValue() > config.getMinWeight()) {
                indices.add(entry.getKey());
                values.add(entry.getValue());
            }
        }

        // Top-K选择
        if (config.getTopK() > 0 && indices.size() > config.getTopK()) {
            List<Pair<Integer, Float>> pairs = new ArrayList<>();
            for (int i = 0; i < indices.size(); i++) {
                pairs.add(Pair.of(indices.get(i), values.get(i)));
            }

            pairs.sort((a, b) -> Float.compare(b.getRight(), a.getRight()));
            pairs = pairs.subList(0, config.getTopK());

            indices = pairs.stream().map(Pair::getLeft).collect(Collectors.toList());
            values = pairs.stream().map(Pair::getRight).collect(Collectors.toList());
        }

        return SparseVector.builder()
            .indices(indices)
            .values(values)
            .dimension(bgeM3Model.getVocabSize())
            .sparsity(calculateSparsity(indices.size(), bgeM3Model.getVocabSize()))
            .build();
    }

    @Override
    public float[] generateDenseEmbedding(String text, DenseEmbeddingConfig config) {
        // 使用BGE-M3生成稠密向量
        BgeM3Output output = bgeM3Model.encode(text);
        return output.getDenseVector();
    }

    @Override
    public List<SparseVector> batchGenerateSparseEmbedding(List<String> texts,
                                                         SparseEmbeddingConfig config) {
        List<BgeM3Output> outputs = bgeM3Model.batchEncode(texts);
        return outputs.stream()
            .map(output -> convertToSparseVector(output.getSparseVector(), config))
            .collect(Collectors.toList());
    }

    @Override
    public List<float[]> batchGenerateDenseEmbedding(List<String> texts,
                                                   DenseEmbeddingConfig config) {
        List<BgeM3Output> outputs = bgeM3Model.batchEncode(texts);
        return outputs.stream()
            .map(BgeM3Output::getDenseVector)
            .collect(Collectors.toList());
    }

    private SparseVector convertToSparseVector(Map<Integer, Float> sparseWeights,
                                             SparseEmbeddingConfig config) {
        List<Integer> indices = new ArrayList<>();
        List<Float> values = new ArrayList<>();

        for (Map.Entry<Integer, Float> entry : sparseWeights.entrySet()) {
            if (entry.getValue() > config.getMinWeight()) {
                indices.add(entry.getKey());
                values.add(entry.getValue());
            }
        }

        return SparseVector.builder()
            .indices(indices)
            .values(values)
            .dimension(bgeM3Model.getVocabSize())
            .sparsity(calculateSparsity(indices.size(), bgeM3Model.getVocabSize()))
            .build();
    }

    private double calculateSparsity(int nonZeroCount, int totalDimension) {
        return 1.0 - (double) nonZeroCount / totalDimension;
    }

    @Override
    public int getVocabSize() {
        return bgeM3Model.getVocabSize();
    }

    @Override
    public int getDimension() {
        return bgeM3Model.getDimension();
    }

    @Override
    public String getModelName() {
        return "BGE-M3";
    }

    @Override
    public boolean supportsLanguage(String language) {
        return true; // BGE-M3支持多语言
    }
}
```

#### 1.4 阿里云稠密向量服务实现
```java
@Component
public class AliCloudEmbeddingService implements EmbeddingService {
    
    @Autowired
    private AliCloudNlpClient nlpClient;
    
    @Value("${rag.embedding.alicloud.model:text-embedding-v1}")
    private String modelName;
    
    @Override
    public float[] generateEmbedding(String text, EmbeddingConfig config) {
        try {
            // 文本预处理
            String processedText = preprocessText(text, config);
            
            // 调用阿里云API
            EmbeddingRequest request = EmbeddingRequest.builder()
                .text(processedText)
                .model(modelName)
                .build();
            
            EmbeddingResponse response = nlpClient.generateEmbedding(request);
            
            // 后处理向量
            return postprocessVector(response.getEmbedding(), config);
            
        } catch (Exception e) {
            log.error("Failed to generate embedding for text: {}", text.substring(0, Math.min(100, text.length())), e);
            throw new EmbeddingGenerationException("Failed to generate embedding", e);
        }
    }
    
    @Override
    public List<float[]> batchGenerateEmbedding(List<String> texts, EmbeddingConfig config) {
        List<float[]> embeddings = new ArrayList<>();
        
        // 分批处理
        int batchSize = config.getBatchSize();
        for (int i = 0; i < texts.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, texts.size());
            List<String> batch = texts.subList(i, endIndex);
            
            try {
                List<float[]> batchEmbeddings = processBatch(batch, config);
                embeddings.addAll(batchEmbeddings);
                
                // 控制API调用频率
                if (endIndex < texts.size()) {
                    Thread.sleep(config.getBatchDelay());
                }
                
            } catch (Exception e) {
                log.error("Failed to process batch {}-{}", i, endIndex, e);
                // 降级处理：逐个生成
                for (String text : batch) {
                    try {
                        embeddings.add(generateEmbedding(text, config));
                    } catch (Exception ex) {
                        log.error("Failed to generate embedding for single text", ex);
                        embeddings.add(new float[getDimension()]); // 零向量作为占位符
                    }
                }
            }
        }
        
        return embeddings;
    }
    
    private String preprocessText(String text, EmbeddingConfig config) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        String processed = text;
        
        // 长度截断
        if (processed.length() > config.getMaxTextLength()) {
            processed = truncateText(processed, config.getMaxTextLength());
        }
        
        // 特殊字符处理
        if (config.isCleanSpecialChars()) {
            processed = cleanSpecialCharacters(processed);
        }
        
        // 标准化空白字符
        processed = normalizeWhitespace(processed);
        
        return processed;
    }
    
    private float[] postprocessVector(float[] vector, EmbeddingConfig config) {
        if (config.isNormalize()) {
            return normalizeVector(vector);
        }
        return vector;
    }
}
```

#### 1.3 多语言向量化支持
```java
@Component
public class MultilingualEmbeddingService implements EmbeddingService {
    
    private final Map<String, EmbeddingService> languageSpecificServices;
    
    @Autowired
    private LanguageDetectionService languageDetector;
    
    @Override
    public float[] generateEmbedding(String text, EmbeddingConfig config) {
        // 检测语言
        String language = languageDetector.detectLanguage(text);
        
        // 选择对应的向量化服务
        EmbeddingService service = getServiceForLanguage(language);
        
        // 语言特定的预处理
        String processedText = preprocessForLanguage(text, language, config);
        
        return service.generateEmbedding(processedText, config);
    }
    
    private EmbeddingService getServiceForLanguage(String language) {
        EmbeddingService service = languageSpecificServices.get(language);
        if (service != null) {
            return service;
        }
        
        // 回退到通用服务
        return languageSpecificServices.get("universal");
    }
    
    private String preprocessForLanguage(String text, String language, EmbeddingConfig config) {
        switch (language) {
            case "zh":
                return preprocessChinese(text, config);
            case "en":
                return preprocessEnglish(text, config);
            case "ja":
                return preprocessJapanese(text, config);
            default:
                return preprocessGeneric(text, config);
        }
    }
    
    private String preprocessChinese(String text, EmbeddingConfig config) {
        // 中文特定处理
        // 1. 繁简转换
        if (config.isConvertTraditionalToSimplified()) {
            text = convertToSimplifiedChinese(text);
        }
        
        // 2. 分词处理（如果需要）
        if (config.isEnableSegmentation()) {
            text = segmentChinese(text);
        }
        
        return text;
    }
}
```

### 2. 向量质量优化

#### 2.1 文本预处理管道
```java
@Component
public class TextPreprocessingPipeline {
    
    private final List<TextProcessor> processors;
    
    public TextPreprocessingPipeline() {
        this.processors = Arrays.asList(
            new LengthValidator(),
            new EncodingNormalizer(),
            new SpecialCharacterCleaner(),
            new WhitespaceNormalizer(),
            new StopWordFilter(),
            new TextNormalizer()
        );
    }
    
    public String preprocess(String text, PreprocessingConfig config) {
        String result = text;
        
        for (TextProcessor processor : processors) {
            if (processor.isEnabled(config)) {
                result = processor.process(result, config);
            }
        }
        
        return result;
    }
}

public interface TextProcessor {
    String process(String text, PreprocessingConfig config);
    boolean isEnabled(PreprocessingConfig config);
    String getProcessorName();
}

@Component
public class SpecialCharacterCleaner implements TextProcessor {
    
    @Override
    public String process(String text, PreprocessingConfig config) {
        if (text == null) return "";
        
        String result = text;
        
        // 移除控制字符
        result = result.replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "");
        
        // 标准化引号
        result = result.replaceAll("[""''`]", "\"");
        
        // 标准化破折号
        result = result.replaceAll("[—–]", "-");
        
        // 移除零宽字符
        result = result.replaceAll("[\\u200B\\u200C\\u200D\\uFEFF]", "");
        
        return result;
    }
    
    @Override
    public boolean isEnabled(PreprocessingConfig config) {
        return config.isCleanSpecialCharacters();
    }
}
```

#### 2.2 向量后处理优化
```java
@Component
public class VectorPostProcessor {
    
    public float[] postProcess(float[] vector, PostProcessingConfig config) {
        float[] result = vector.clone();
        
        // L2归一化
        if (config.isNormalize()) {
            result = normalizeL2(result);
        }
        
        // 维度降维
        if (config.isEnableDimensionReduction()) {
            result = reduceDimension(result, config.getTargetDimension());
        }
        
        // 量化处理
        if (config.isEnableQuantization()) {
            result = quantize(result, config.getQuantizationBits());
        }
        
        return result;
    }
    
    private float[] normalizeL2(float[] vector) {
        double norm = 0.0;
        for (float value : vector) {
            norm += value * value;
        }
        norm = Math.sqrt(norm);
        
        if (norm == 0.0) {
            return vector;
        }
        
        float[] normalized = new float[vector.length];
        for (int i = 0; i < vector.length; i++) {
            normalized[i] = (float) (vector[i] / norm);
        }
        
        return normalized;
    }
    
    private float[] reduceDimension(float[] vector, int targetDimension) {
        if (vector.length <= targetDimension) {
            return vector;
        }
        
        // 使用PCA或随机投影进行降维
        return pcaReduce(vector, targetDimension);
    }
    
    private float[] quantize(float[] vector, int bits) {
        // 向量量化以减少存储空间
        float min = Float.MAX_VALUE;
        float max = Float.MIN_VALUE;
        
        for (float value : vector) {
            min = Math.min(min, value);
            max = Math.max(max, value);
        }
        
        int levels = (1 << bits) - 1;
        float scale = (max - min) / levels;
        
        float[] quantized = new float[vector.length];
        for (int i = 0; i < vector.length; i++) {
            int level = Math.round((vector[i] - min) / scale);
            quantized[i] = min + level * scale;
        }
        
        return quantized;
    }
}
```

### 3. 批量处理优化

#### 3.1 异步批处理引擎
```java
@Component
public class AsyncEmbeddingProcessor {
    
    @Autowired
    private EmbeddingService embeddingService;
    
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);
    
    public CompletableFuture<List<EmbeddingResult>> processAsync(List<DocumentChunk> chunks) {
        return CompletableFuture.supplyAsync(() -> {
            List<EmbeddingResult> results = new ArrayList<>();
            
            // 分批处理
            List<List<DocumentChunk>> batches = partitionChunks(chunks, 50);
            
            List<CompletableFuture<List<EmbeddingResult>>> futures = batches.stream()
                .map(batch -> processBatchAsync(batch))
                .collect(Collectors.toList());
            
            // 等待所有批次完成
            for (CompletableFuture<List<EmbeddingResult>> future : futures) {
                try {
                    results.addAll(future.get());
                } catch (Exception e) {
                    log.error("Failed to process embedding batch", e);
                }
            }
            
            return results;
        }, executorService);
    }
    
    private CompletableFuture<List<EmbeddingResult>> processBatchAsync(List<DocumentChunk> batch) {
        return CompletableFuture.supplyAsync(() -> {
            List<EmbeddingResult> results = new ArrayList<>();
            
            try {
                List<String> texts = batch.stream()
                    .map(DocumentChunk::getContent)
                    .collect(Collectors.toList());
                
                List<float[]> embeddings = embeddingService.batchGenerateEmbedding(
                    texts, EmbeddingConfig.defaultConfig());
                
                for (int i = 0; i < batch.size(); i++) {
                    DocumentChunk chunk = batch.get(i);
                    float[] embedding = embeddings.get(i);
                    
                    EmbeddingResult result = EmbeddingResult.builder()
                        .chunkId(chunk.getId())
                        .embedding(embedding)
                        .status(EmbeddingStatus.SUCCESS)
                        .processingTime(System.currentTimeMillis())
                        .build();
                    
                    results.add(result);
                }
                
            } catch (Exception e) {
                log.error("Failed to process embedding batch", e);
                // 创建失败结果
                for (DocumentChunk chunk : batch) {
                    EmbeddingResult result = EmbeddingResult.builder()
                        .chunkId(chunk.getId())
                        .status(EmbeddingStatus.FAILED)
                        .errorMessage(e.getMessage())
                        .build();
                    results.add(result);
                }
            }
            
            return results;
        }, executorService);
    }
}
```

#### 3.2 失败重试机制
```java
@Component
public class EmbeddingRetryHandler {
    
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long BASE_DELAY_MS = 1000;
    
    public float[] generateEmbeddingWithRetry(String text, EmbeddingConfig config) {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                return embeddingService.generateEmbedding(text, config);
                
            } catch (RateLimitException e) {
                // API限流，等待更长时间
                long delay = calculateBackoffDelay(attempt) * 2;
                waitWithBackoff(delay);
                lastException = e;
                
            } catch (TemporaryServiceException e) {
                // 临时服务异常，指数退避
                long delay = calculateBackoffDelay(attempt);
                waitWithBackoff(delay);
                lastException = e;
                
            } catch (PermanentException e) {
                // 永久性错误，不重试
                throw e;
                
            } catch (Exception e) {
                lastException = e;
                if (attempt == MAX_RETRY_ATTEMPTS) {
                    break;
                }
                waitWithBackoff(calculateBackoffDelay(attempt));
            }
        }
        
        throw new EmbeddingGenerationException(
            "Failed to generate embedding after " + MAX_RETRY_ATTEMPTS + " attempts", 
            lastException);
    }
    
    private long calculateBackoffDelay(int attempt) {
        return BASE_DELAY_MS * (long) Math.pow(2, attempt - 1);
    }
    
    private void waitWithBackoff(long delayMs) {
        try {
            Thread.sleep(delayMs);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Interrupted during backoff", e);
        }
    }
}
```



## 数据模型

### 混合向量数据模型
```java
@Data
@Builder
public class HybridVector {
    private String text;
    private float[] denseVector;
    private SparseVector sparseVector;
    private String denseModel;
    private String sparseModel;
    private long timestamp;
    private Map<String, Object> metadata;
}

@Data
@Builder
public class SparseVector {
    private List<Integer> indices;
    private List<Float> values;
    private int dimension;
    private double sparsity;

    // 转换为Milvus SparseFloatVector格式
    public SparseFloatVector toMilvusFormat() {
        List<Long> longIndices = indices.stream()
            .map(Integer::longValue)
            .collect(Collectors.toList());
        return new SparseFloatVector(longIndices, values);
    }

    // 计算稀疏向量的L2范数
    public double getL2Norm() {
        return Math.sqrt(values.stream()
            .mapToDouble(v -> v * v)
            .sum());
    }

    // 获取非零元素数量
    public int getNonZeroCount() {
        return indices.size();
    }
}

@Data
@Builder
public class BgeM3Output {
    private float[] denseVector;
    private Map<Integer, Float> sparseVector;
    private String text;
    private long processingTime;
}

@Data
@Builder
public class VectorBatch {
    private List<String> texts;
    private List<HybridVector> hybridVectors;
    private List<float[]> denseVectors;
    private List<SparseVector> sparseVectors;
    private VectorBatchStats stats;
}

@Data
@Builder
public class VectorBatchStats {
    private int totalTexts;
    private int successCount;
    private int failureCount;
    private long totalProcessingTime;
    private long avgProcessingTime;
    private double avgDenseVectorNorm;
    private double avgSparseVectorSparsity;
}
```

### 配置数据模型
```java
@Data
@Builder
public class HybridEmbeddingConfig {
    private DenseEmbeddingConfig denseConfig;
    private SparseEmbeddingConfig sparseConfig;
    private boolean enableParallelProcessing;
    private int timeoutMs;
}

@Data
@Builder
public class DenseEmbeddingConfig {
    private String modelName;
    private int dimension;
    private boolean normalize;
    private int maxTextLength;
    private Map<String, Object> modelParams;
}

@Data
@Builder
public class SparseEmbeddingConfig {
    private String modelName;
    private float minWeight;
    private int topK;
    private int vocabSize;
    private boolean enableCompression;
    private Map<String, Object> modelParams;
}
```

## 配置与监控

### 混合向量化配置
```java
@ConfigurationProperties(prefix = "rag.embedding")
@Data
public class HybridEmbeddingConfig {

    // 基础配置
    private boolean enableHybridVectorization = true;
    private boolean enableParallelProcessing = true;
    private int maxTextLength = 8192;

    // 稠密向量配置
    private DenseVectorConfig denseVector = new DenseVectorConfig();

    // 稀疏向量配置
    private SparseVectorConfig sparseVector = new SparseVectorConfig();

    // 批处理配置
    private int batchSize = 50;
    private long batchDelay = 100; // ms
    private int maxConcurrentBatches = 5;
    private int timeoutMs = 30000;

    // 预处理配置
    private boolean cleanSpecialCharacters = true;
    private boolean enableStopWordFiltering = false;
    private boolean convertTraditionalToSimplified = true;

    // 重试配置
    private int maxRetryAttempts = 3;
    private long baseRetryDelay = 1000; // ms

    // 缓存配置
    private boolean enableCaching = true;
    private long cacheExpiration = 3600; // seconds
    private boolean enableSeparateCache = true; // 稠密和稀疏向量分别缓存

    @Data
    public static class DenseVectorConfig {
        private String defaultModel = "bge-large-zh-v1.5";
        private List<String> availableModels = Arrays.asList(
            "text-embedding-3-large",
            "text-embedding-ada-002",
            "bge-large-zh-v1.5",
            "m3e-large"
        );
        private int dimension = 1024;
        private boolean normalize = true;
        private String metricType = "COSINE";
        private Map<String, Object> modelParams = new HashMap<>();
    }

    @Data
    public static class SparseVectorConfig {
        private String defaultModel = "SPLADE";
        private List<String> availableModels = Arrays.asList(
            "SPLADE",
            "BGE-M3-sparse",
            "custom-sparse"
        );
        private float minWeight = 0.01f;
        private int topK = 200; // 保留Top-K个最重要的维度
        private int vocabSize = 30522; // BERT词汇表大小
        private boolean enableCompression = true;
        private String metricType = "IP"; // Inner Product
        private Map<String, Object> modelParams = new HashMap<>();
    }
}
```

### 混合向量缓存服务
```java
@Component
public class VectorCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${rag.embedding.cacheExpiration:3600}")
    private long cacheExpiration;

    private static final String DENSE_VECTOR_PREFIX = "dense_vector:";
    private static final String SPARSE_VECTOR_PREFIX = "sparse_vector:";
    private static final String HYBRID_VECTOR_PREFIX = "hybrid_vector:";

    public void putDenseVector(String key, float[] vector) {
        String cacheKey = DENSE_VECTOR_PREFIX + key;
        redisTemplate.opsForValue().set(cacheKey, vector, cacheExpiration, TimeUnit.SECONDS);
    }

    public float[] getDenseVector(String key) {
        String cacheKey = DENSE_VECTOR_PREFIX + key;
        return (float[]) redisTemplate.opsForValue().get(cacheKey);
    }

    public void putSparseVector(String key, SparseVector vector) {
        String cacheKey = SPARSE_VECTOR_PREFIX + key;
        redisTemplate.opsForValue().set(cacheKey, vector, cacheExpiration, TimeUnit.SECONDS);
    }

    public SparseVector getSparseVector(String key) {
        String cacheKey = SPARSE_VECTOR_PREFIX + key;
        return (SparseVector) redisTemplate.opsForValue().get(cacheKey);
    }

    public void putHybridVector(String key, HybridVector vector) {
        String cacheKey = HYBRID_VECTOR_PREFIX + key;
        redisTemplate.opsForValue().set(cacheKey, vector, cacheExpiration, TimeUnit.SECONDS);
    }

    public HybridVector getHybridVector(String key) {
        String cacheKey = HYBRID_VECTOR_PREFIX + key;
        return (HybridVector) redisTemplate.opsForValue().get(cacheKey);
    }

    public void batchPutHybridVectors(Map<String, HybridVector> vectors) {
        Map<String, Object> cacheMap = new HashMap<>();
        for (Map.Entry<String, HybridVector> entry : vectors.entrySet()) {
            String cacheKey = HYBRID_VECTOR_PREFIX + entry.getKey();
            cacheMap.put(cacheKey, entry.getValue());
        }

        redisTemplate.opsForValue().multiSet(cacheMap);

        // 设置过期时间
        for (String cacheKey : cacheMap.keySet()) {
            redisTemplate.expire(cacheKey, cacheExpiration, TimeUnit.SECONDS);
        }
    }

    public Map<String, HybridVector> batchGetHybridVectors(List<String> keys) {
        List<String> cacheKeys = keys.stream()
            .map(key -> HYBRID_VECTOR_PREFIX + key)
            .collect(Collectors.toList());

        List<Object> values = redisTemplate.opsForValue().multiGet(cacheKeys);

        Map<String, HybridVector> result = new HashMap<>();
        for (int i = 0; i < keys.size(); i++) {
            if (values.get(i) != null) {
                result.put(keys.get(i), (HybridVector) values.get(i));
            }
        }

        return result;
    }

    public void clearCache() {
        Set<String> keys = redisTemplate.keys(DENSE_VECTOR_PREFIX + "*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }

        keys = redisTemplate.keys(SPARSE_VECTOR_PREFIX + "*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }

        keys = redisTemplate.keys(HYBRID_VECTOR_PREFIX + "*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }

    public CacheStats getCacheStats() {
        long denseVectorCount = countKeys(DENSE_VECTOR_PREFIX + "*");
        long sparseVectorCount = countKeys(SPARSE_VECTOR_PREFIX + "*");
        long hybridVectorCount = countKeys(HYBRID_VECTOR_PREFIX + "*");

        return CacheStats.builder()
            .denseVectorCount(denseVectorCount)
            .sparseVectorCount(sparseVectorCount)
            .hybridVectorCount(hybridVectorCount)
            .totalCount(denseVectorCount + sparseVectorCount + hybridVectorCount)
            .build();
    }

    private long countKeys(String pattern) {
        Set<String> keys = redisTemplate.keys(pattern);
        return keys != null ? keys.size() : 0;
    }
}

@Data
@Builder
public class CacheStats {
    private long denseVectorCount;
    private long sparseVectorCount;
    private long hybridVectorCount;
    private long totalCount;
    private double hitRate;
    private long totalRequests;
    private long cacheHits;
}
```

### 性能监控
```java
@Component
public class EmbeddingMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    private final Counter embeddingRequestCounter;
    private final Timer embeddingLatencyTimer;
    private final Gauge embeddingQueueSize;
    
    public EmbeddingMetricsCollector(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.embeddingRequestCounter = Counter.builder("embedding.requests.total")
            .description("Total number of embedding requests")
            .register(meterRegistry);
        this.embeddingLatencyTimer = Timer.builder("embedding.latency")
            .description("Embedding generation latency")
            .register(meterRegistry);
        this.embeddingQueueSize = Gauge.builder("embedding.queue.size")
            .description("Current embedding queue size")
            .register(meterRegistry, this, EmbeddingMetricsCollector::getCurrentQueueSize);
    }
    
    public void recordEmbeddingRequest(String model, String status) {
        embeddingRequestCounter.increment(
            Tags.of("model", model, "status", status));
    }
    
    public void recordEmbeddingLatency(long latencyMs, String model) {
        embeddingLatencyTimer.record(latencyMs, TimeUnit.MILLISECONDS,
            Tags.of("model", model));
    }
    
    private double getCurrentQueueSize() {
        // 返回当前队列大小
        return embeddingQueueManager.getQueueSize();
    }
}
```

## 实体向量化服务

### 1. 实体向量化服务接口
```java
public interface EntityVectorizationService {
    EntityVector vectorizeEntity(RecognizedEntity entity, EntityVectorConfig config);
    List<EntityVector> vectorizeEntities(List<RecognizedEntity> entities, EntityVectorConfig config);
    RelationshipVector vectorizeRelationship(EntityRelationship relationship, RelationshipVectorConfig config);
    KnowledgeGraphVector vectorizeWithKnowledgeGraph(DocumentChunk chunk, KnowledgeGraphData kgData, KGVectorConfig config);
}

@Service
public class EntityVectorizationServiceImpl implements EntityVectorizationService {

    @Autowired
    private DenseVectorService denseVectorService;

    @Autowired
    private SparseVectorService sparseVectorService;

    @Autowired
    private KnowledgeGraphService knowledgeGraphService;

    @Override
    public EntityVector vectorizeEntity(RecognizedEntity entity, EntityVectorConfig config) {
        // 1. 构建实体的上下文文本
        String entityContext = buildEntityContext(entity, config);

        // 2. 生成实体的稠密向量
        float[] denseVector = denseVectorService.generateDenseVector(entityContext);

        // 3. 生成实体的稀疏向量
        SparseFloatVector sparseVector = sparseVectorService.generateSparseVector(entityContext);

        // 4. 获取实体的知识图谱信息
        EntityKnowledgeInfo kgInfo = knowledgeGraphService.getEntityInfo(entity);

        // 5. 融合知识图谱信息
        float[] enhancedDenseVector = enhanceWithKnowledgeGraph(denseVector, kgInfo, config);

        return EntityVector.builder()
            .entityId(entity.getId())
            .entityType(entity.getType())
            .entityText(entity.getText())
            .denseVector(enhancedDenseVector)
            .sparseVector(sparseVector)
            .knowledgeInfo(kgInfo)
            .confidence(entity.getConfidence())
            .build();
    }

    @Override
    public List<EntityVector> vectorizeEntities(List<RecognizedEntity> entities, EntityVectorConfig config) {
        // 批量处理实体向量化
        List<EntityVector> entityVectors = new ArrayList<>();

        // 按类型分组处理
        Map<String, List<RecognizedEntity>> entitiesByType = entities.stream()
            .collect(Collectors.groupingBy(RecognizedEntity::getType));

        for (Map.Entry<String, List<RecognizedEntity>> entry : entitiesByType.entrySet()) {
            String entityType = entry.getKey();
            List<RecognizedEntity> typeEntities = entry.getValue();

            // 为每种类型的实体使用专门的向量化策略
            EntityVectorConfig typeConfig = config.getConfigForType(entityType);

            for (RecognizedEntity entity : typeEntities) {
                EntityVector vector = vectorizeEntity(entity, typeConfig);
                entityVectors.add(vector);
            }
        }

        return entityVectors;
    }

    @Override
    public RelationshipVector vectorizeRelationship(EntityRelationship relationship, RelationshipVectorConfig config) {
        // 1. 构建关系的文本表示
        String relationshipText = buildRelationshipText(relationship, config);

        // 2. 生成关系向量
        float[] relationshipVector = denseVectorService.generateDenseVector(relationshipText);

        // 3. 获取关系的知识图谱信息
        RelationshipKnowledgeInfo kgInfo = knowledgeGraphService.getRelationshipInfo(relationship);

        // 4. 融合实体向量信息
        float[] enhancedVector = enhanceRelationshipVector(
            relationshipVector,
            relationship.getSourceEntity(),
            relationship.getTargetEntity(),
            config
        );

        return RelationshipVector.builder()
            .relationshipId(relationship.getId())
            .relationType(relationship.getType())
            .sourceEntityId(relationship.getSourceEntity().getId())
            .targetEntityId(relationship.getTargetEntity().getId())
            .vector(enhancedVector)
            .strength(relationship.getStrength())
            .confidence(relationship.getConfidence())
            .knowledgeInfo(kgInfo)
            .build();
    }

    @Override
    public KnowledgeGraphVector vectorizeWithKnowledgeGraph(DocumentChunk chunk, KnowledgeGraphData kgData, KGVectorConfig config) {
        // 1. 生成文档块的基础向量
        float[] baseDenseVector = denseVectorService.generateDenseVector(chunk.getContent());
        SparseFloatVector baseSparseVector = sparseVectorService.generateSparseVector(chunk.getContent());

        // 2. 向量化文档块中的实体
        List<EntityVector> entityVectors = vectorizeEntities(kgData.getEntities(), config.getEntityConfig());

        // 3. 向量化实体间的关系
        List<RelationshipVector> relationshipVectors = new ArrayList<>();
        for (EntityRelationship relationship : kgData.getRelationships()) {
            RelationshipVector relVector = vectorizeRelationship(relationship, config.getRelationshipConfig());
            relationshipVectors.add(relVector);
        }

        // 4. 融合所有向量信息
        float[] enhancedDenseVector = fuseVectors(baseDenseVector, entityVectors, relationshipVectors, config);
        SparseFloatVector enhancedSparseVector = fuseSparseVectors(baseSparseVector, entityVectors, config);

        return KnowledgeGraphVector.builder()
            .chunkId(chunk.getId())
            .baseDenseVector(baseDenseVector)
            .baseSparseVector(baseSparseVector)
            .enhancedDenseVector(enhancedDenseVector)
            .enhancedSparseVector(enhancedSparseVector)
            .entityVectors(entityVectors)
            .relationshipVectors(relationshipVectors)
            .knowledgeGraphData(kgData)
            .build();
    }
}

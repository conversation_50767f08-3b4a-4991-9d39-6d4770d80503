# 知识图谱检索模块

## 模块概述

知识图谱检索模块是检索系统的知识推理引擎，负责基于Neo4j知识图谱进行实体查询、关系推理和图谱增强检索。该模块与传统的向量检索形成互补，通过结构化知识和语义推理，显著提升RAG系统对复杂查询的理解和回答能力。

## 核心功能

### 1. 实体识别与图谱查询
- **实体识别**：从用户查询中识别关键实体
- **实体链接**：将识别的实体链接到知识图谱中的节点
- **实体查询**：基于实体进行精确的图谱查询
- **实体扩展**：发现相关实体和同义实体

### 2. 关系推理与路径发现
- **关系识别**：识别查询中的关系模式
- **路径查询**：在知识图谱中查找实体间的路径
- **多跳推理**：支持多层关系的推理查询
- **路径排序**：基于路径重要性进行排序

### 3. 图谱增强检索
- **图谱上下文**：为检索结果提供知识图谱上下文
- **关系增强**：基于实体关系增强检索结果
- **知识补全**：补充向量检索遗漏的结构化知识
- **推理验证**：验证检索结果的逻辑一致性

### 4. 混合检索融合
- **三重融合**：稠密向量+稀疏向量+知识图谱的融合
- **权重调节**：动态调整不同检索方式的权重
- **结果合并**：智能合并不同检索源的结果
- **一致性检查**：确保融合结果的一致性

## 技术架构

### 1. 知识图谱查询服务
```java
public interface KnowledgeGraphQueryService {
    List<Entity> findEntities(String query, EntityQueryConfig config);
    List<Relationship> findRelationships(List<Entity> entities, RelationshipQueryConfig config);
    List<Path> findPaths(Entity source, Entity target, PathQueryConfig config);
    GraphContext buildGraphContext(List<Entity> entities, GraphContextConfig config);
}

@Service
public class Neo4jKnowledgeGraphQueryService implements KnowledgeGraphQueryService {
    
    @Autowired
    private Neo4jTemplate neo4jTemplate;
    
    @Autowired
    private EntityRecognitionService entityRecognitionService;
    
    @Override
    public List<Entity> findEntities(String query, EntityQueryConfig config) {
        // 实体识别
        List<RecognizedEntity> recognizedEntities = entityRecognitionService.recognize(query);
        
        List<Entity> entities = new ArrayList<>();
        for (RecognizedEntity recognized : recognizedEntities) {
            // 实体链接到知识图谱
            String cypherQuery = buildEntityLinkingQuery(recognized, config);
            List<Entity> linkedEntities = neo4jTemplate.query(cypherQuery, Entity.class);
            entities.addAll(linkedEntities);
        }
        
        return entities;
    }
    
    @Override
    public List<Relationship> findRelationships(List<Entity> entities, RelationshipQueryConfig config) {
        if (entities.size() < 2) {
            return Collections.emptyList();
        }
        
        String cypherQuery = buildRelationshipQuery(entities, config);
        return neo4jTemplate.query(cypherQuery, Relationship.class);
    }
    
    @Override
    public List<Path> findPaths(Entity source, Entity target, PathQueryConfig config) {
        String cypherQuery = String.format(
            "MATCH path = (source:Entity {id: '%s'})-[*1..%d]-(target:Entity {id: '%s'}) " +
            "RETURN path ORDER BY length(path) LIMIT %d",
            source.getId(), config.getMaxHops(), target.getId(), config.getMaxPaths()
        );
        
        return neo4jTemplate.query(cypherQuery, Path.class);
    }
    
    @Override
    public GraphContext buildGraphContext(List<Entity> entities, GraphContextConfig config) {
        // 构建实体的图谱上下文
        List<Relationship> relationships = findRelationships(entities, config.getRelationshipConfig());
        List<Entity> relatedEntities = findRelatedEntities(entities, config.getExpansionConfig());
        
        return GraphContext.builder()
            .entities(entities)
            .relatedEntities(relatedEntities)
            .relationships(relationships)
            .build();
    }
    
    private String buildEntityLinkingQuery(RecognizedEntity recognized, EntityQueryConfig config) {
        return String.format(
            "MATCH (e:Entity) " +
            "WHERE e.name =~ '(?i).*%s.*' OR e.alias =~ '(?i).*%s.*' " +
            "RETURN e ORDER BY e.importance DESC LIMIT %d",
            recognized.getText(), recognized.getText(), config.getMaxResults()
        );
    }
    
    private String buildRelationshipQuery(List<Entity> entities, RelationshipQueryConfig config) {
        String entityIds = entities.stream()
            .map(e -> "'" + e.getId() + "'")
            .collect(Collectors.joining(","));
        
        return String.format(
            "MATCH (e1:Entity)-[r]-(e2:Entity) " +
            "WHERE e1.id IN [%s] AND e2.id IN [%s] " +
            "RETURN r, e1, e2 ORDER BY r.weight DESC LIMIT %d",
            entityIds, entityIds, config.getMaxResults()
        );
    }
}
```

### 2. 图谱增强检索服务
```java
public interface GraphEnhancedRetrievalService {
    GraphRetrievalResult retrieve(SearchQuery query, GraphRetrievalConfig config);
    List<EnhancedResult> enhanceWithGraph(List<RetrievalResult> vectorResults, GraphContext context);
    HybridRetrievalResult hybridRetrieve(SearchQuery query, HybridRetrievalConfig config);
}

@Service
public class GraphEnhancedRetrievalServiceImpl implements GraphEnhancedRetrievalService {
    
    @Autowired
    private KnowledgeGraphQueryService kgQueryService;
    
    @Autowired
    private VectorRetrievalService vectorRetrievalService;
    
    @Override
    public GraphRetrievalResult retrieve(SearchQuery query, GraphRetrievalConfig config) {
        // 1. 实体识别和图谱查询
        List<Entity> entities = kgQueryService.findEntities(query.getText(), config.getEntityConfig());
        
        // 2. 关系查询
        List<Relationship> relationships = kgQueryService.findRelationships(entities, config.getRelationshipConfig());
        
        // 3. 路径发现
        List<Path> paths = findRelevantPaths(entities, config.getPathConfig());
        
        // 4. 构建图谱上下文
        GraphContext context = kgQueryService.buildGraphContext(entities, config.getContextConfig());
        
        // 5. 基于图谱的文档检索
        List<Document> graphDocuments = retrieveDocumentsByGraph(context, config);
        
        return GraphRetrievalResult.builder()
            .entities(entities)
            .relationships(relationships)
            .paths(paths)
            .context(context)
            .documents(graphDocuments)
            .build();
    }
    
    @Override
    public List<EnhancedResult> enhanceWithGraph(List<RetrievalResult> vectorResults, GraphContext context) {
        List<EnhancedResult> enhancedResults = new ArrayList<>();
        
        for (RetrievalResult result : vectorResults) {
            // 为每个向量检索结果添加图谱上下文
            List<Entity> relatedEntities = findRelatedEntitiesInDocument(result.getDocument(), context);
            List<Relationship> relatedRelationships = findRelatedRelationships(relatedEntities, context);
            
            EnhancedResult enhanced = EnhancedResult.builder()
                .originalResult(result)
                .relatedEntities(relatedEntities)
                .relatedRelationships(relatedRelationships)
                .graphScore(calculateGraphScore(result, context))
                .build();
            
            enhancedResults.add(enhanced);
        }
        
        return enhancedResults;
    }
    
    @Override
    public HybridRetrievalResult hybridRetrieve(SearchQuery query, HybridRetrievalConfig config) {
        // 并行执行向量检索和图谱检索
        CompletableFuture<List<RetrievalResult>> vectorFuture = CompletableFuture.supplyAsync(() ->
            vectorRetrievalService.retrieve(query, config.getVectorConfig())
        );
        
        CompletableFuture<GraphRetrievalResult> graphFuture = CompletableFuture.supplyAsync(() ->
            retrieve(query, config.getGraphConfig())
        );
        
        // 等待两个检索完成
        List<RetrievalResult> vectorResults = vectorFuture.join();
        GraphRetrievalResult graphResult = graphFuture.join();
        
        // 融合检索结果
        List<EnhancedResult> enhancedResults = enhanceWithGraph(vectorResults, graphResult.getContext());
        
        // 重新排序
        List<FusedResult> fusedResults = fuseResults(enhancedResults, graphResult, config.getFusionConfig());
        
        return HybridRetrievalResult.builder()
            .vectorResults(vectorResults)
            .graphResult(graphResult)
            .enhancedResults(enhancedResults)
            .fusedResults(fusedResults)
            .build();
    }
    
    private List<Document> retrieveDocumentsByGraph(GraphContext context, GraphRetrievalConfig config) {
        // 基于图谱上下文检索相关文档
        List<String> entityNames = context.getEntities().stream()
            .map(Entity::getName)
            .collect(Collectors.toList());
        
        // 构建图谱查询
        String graphQuery = buildGraphDocumentQuery(entityNames, context.getRelationships());
        
        // 执行文档检索
        return documentRepository.findByGraphQuery(graphQuery, config.getMaxDocuments());
    }
    
    private double calculateGraphScore(RetrievalResult result, GraphContext context) {
        double entityScore = calculateEntityRelevance(result.getDocument(), context.getEntities());
        double relationshipScore = calculateRelationshipRelevance(result.getDocument(), context.getRelationships());
        
        return config.getEntityWeight() * entityScore + config.getRelationshipWeight() * relationshipScore;
    }
}
```

## 流程图

```mermaid
graph TD
    A[用户查询输入] --> B[实体识别与链接]
    B --> C[知识图谱查询]
    
    C --> D[实体查询]
    C --> E[关系查询]
    C --> F[路径发现]
    
    D --> G[图谱上下文构建]
    E --> G
    F --> G
    
    G --> H[并行检索执行]
    H --> I[向量检索分支]
    H --> J[图谱检索分支]
    
    I --> K[稠密向量检索]
    I --> L[稀疏向量检索]
    
    J --> M[基于实体的文档检索]
    J --> N[基于关系的文档检索]
    J --> O[基于路径的文档检索]
    
    K --> P[结果融合]
    L --> P
    M --> P
    N --> P
    O --> P
    
    P --> Q[图谱增强处理]
    Q --> R[一致性验证]
    R --> S[最终排序]
    S --> T[知识图谱检索结果输出]
    T --> U[传递给智能重排模块]

    style A fill:#e1f5fe
    style T fill:#c8e6c9
    style U fill:#fff3e0
```

## 配置参数

### 知识图谱检索配置
```yaml
knowledge_graph:
  enabled: true
  neo4j:
    uri: "bolt://localhost:7687"
    username: "neo4j"
    password: "password"
    database: "knowledge_base"
    
  entity_recognition:
    enabled: true
    confidence_threshold: 0.7
    max_entities: 10
    
  entity_linking:
    enabled: true
    similarity_threshold: 0.8
    max_candidates: 5
    
  relationship_query:
    enabled: true
    max_hops: 3
    max_relationships: 20
    
  path_discovery:
    enabled: true
    max_path_length: 5
    max_paths: 10
    
  graph_retrieval:
    max_documents: 50
    entity_weight: 0.4
    relationship_weight: 0.3
    path_weight: 0.3
    
  hybrid_fusion:
    vector_weight: 0.6
    graph_weight: 0.4
    fusion_strategy: "weighted_sum"
```

## 性能指标

### 图谱查询性能
- **实体识别时间**：<500ms
- **图谱查询时间**：<1秒
- **路径发现时间**：<2秒
- **图谱检索时间**：<3秒

### 检索质量指标
- **实体识别准确率**：>90%
- **实体链接准确率**：>85%
- **关系推理准确率**：>80%
- **混合检索提升**：相比纯向量检索提升15-25%

## 数据模型

### 实体模型
```java
@Node("Entity")
public class Entity {
    @Id
    private String id;
    
    @Property("name")
    private String name;
    
    @Property("type")
    private String type;
    
    @Property("alias")
    private List<String> alias;
    
    @Property("description")
    private String description;
    
    @Property("importance")
    private Double importance;
    
    @Property("vector")
    private float[] vector;
    
    @Relationship(type = "RELATED_TO")
    private List<Relationship> relationships;
}
```

### 关系模型
```java
@RelationshipProperties
public class Relationship {
    @Id
    private String id;
    
    @Property("type")
    private String type;
    
    @Property("weight")
    private Double weight;
    
    @Property("confidence")
    private Double confidence;
    
    @Property("description")
    private String description;
    
    @StartNode
    private Entity source;
    
    @EndNode
    private Entity target;
}
```

## 扩展接口

### 自定义实体识别器
```java
public interface CustomEntityRecognizer {
    List<RecognizedEntity> recognize(String text, EntityRecognitionConfig config);
    boolean supports(String domain);
}
```

### 自定义关系推理器
```java
public interface CustomRelationshipReasoner {
    List<InferredRelationship> infer(List<Entity> entities, ReasoningConfig config);
    boolean supports(String relationshipType);
}
```

本模块通过深度集成Neo4j知识图谱，为RAG系统提供了强大的结构化知识检索和推理能力，与传统向量检索形成完美互补，显著提升了系统对复杂查询的理解和回答质量。

# 文档提取模块 (Document Extraction)

## 方案说明

### 1. 技术方案概述
文档提取模块是RAG系统的数据入口，负责从多种格式的文档中提取结构化内容，为后续的分块和向量化处理提供高质量的文本数据。该模块采用多格式支持、智能预处理和结构化提取的设计理念，确保从各类文档中准确、完整地提取有价值的信息。

### 2. 核心技术特点
- **多格式支持**：支持PDF、Word、Excel、PowerPoint、图片等主流文档格式
- **智能OCR集成**：集成合合OCR API，处理扫描件和图片文档
- **结构化提取**：保留文档的层次结构、表格、列表等格式信息
- **质量控制**：多阶段预处理管道，确保提取内容的质量和准确性
- **元数据增强**：自动提取关键词、实体、主题等元数据信息
- **知识图谱集成**：实体识别、关系抽取，为知识图谱构建提供数据支持

### 3. 技术架构优势
- **可扩展性**：基于策略模式的处理器设计，易于添加新格式支持
- **高可靠性**：多层次错误处理机制
- **高性能**：流水线并行处理，支持大批量文档处理
- **智能化**：结合NLP技术进行内容理解和结构分析
- **知识驱动**：集成知识图谱技术，提升文档理解的深度和准确性

## 流程图

```mermaid
graph TD
    A[原始文档输入] --> B[格式检测阶段]
    B --> C{文档格式判断}

    C -->|PDF文档| D[PDF处理器]
    C -->|Office文档| E[Office处理器]
    C -->|图片文档| F[图片处理器]
    C -->|其他格式| G[通用处理器]

    D --> H{PDF类型判断}
    H -->|文本型PDF| I[文本提取]
    H -->|扫描型PDF| J[OCR识别]

    E --> K[Office内容解析]
    K --> L[段落提取]
    K --> M[表格提取]
    K --> N[样式分析]

    F --> O[图像预处理]
    O --> P[OCR文本识别]
    P --> Q[版面分析]

    I --> R[内容预处理管道]
    J --> R
    L --> R
    M --> R
    N --> R
    P --> R
    Q --> R
    G --> R

    R --> S[噪声过滤]
    S --> T[结构分析]
    T --> U[元数据增强]
    U --> U1[实体识别与抽取]
    U1 --> U2[关系识别与抽取]
    U2 --> U3[知识图谱数据准备]
    U3 --> V[结构化文档输出]

    V --> W[分块模块]

    style A fill:#e1f5fe
    style V fill:#c8e6c9
    style W fill:#fff3e0
```

## 流程步骤说明

### 阶段一：文档格式识别与路由
1. **格式检测**：通过文件扩展名、魔数和内容特征综合判断文档格式
2. **处理器选择**：根据格式类型选择对应的专用处理器
3. **配置加载**：加载格式特定的处理配置参数

### 阶段二：内容提取处理
#### PDF文档处理流程
1. **PDF类型判断**：区分文本型PDF和扫描型PDF
2. **文本型PDF**：使用PDFBox直接提取文本内容，保留页面结构
3. **扫描型PDF**：转换为图像后调用OCR API进行文字识别
4. **页面元数据**：提取页码、字体、布局等结构信息

#### Office文档处理流程
1. **文档解析**：使用Apache POI解析Word/Excel/PowerPoint文档
2. **内容分类**：区分段落、标题、表格、列表等不同内容类型
3. **样式保留**：提取字体、颜色、大小等样式信息
4. **结构重建**：重构文档的层次结构关系

#### 图片文档处理流程
1. **图像预处理**：去噪、对比度调整、倾斜校正等增强处理
2. **OCR识别**：调用合合OCR API进行文字识别
3. **版面分析**：识别文本区域、表格区域、图像区域等
4. **置信度评估**：评估识别结果的可靠性

### 阶段三：内容预处理管道
1. **噪声过滤**：
   - 移除页眉页脚、水印等无关内容
   - 过滤重复内容和空白内容
   - 清理特殊字符和编码问题

2. **结构分析**：
   - 标题层级识别和分类
   - 段落分组和章节划分
   - 表格和列表结构识别

3. **元数据增强**：
   - 关键词提取（使用阿里云NLP）
   - 实体识别（人名、地名、机构名等）
   - 主题分类和语言检测
   - 可读性评分

4. **知识图谱数据准备**：
   - 实体识别与标准化
   - 关系抽取与分类
   - 实体链接与消歧
   - 知识图谱三元组生成

### 阶段四：知识图谱数据准备
1. **实体识别与标准化**：
   - 使用NER模型识别文档中的实体
   - 实体类型分类（人物、地点、组织、概念等）
   - 实体标准化和规范化处理
   - 实体置信度评估

2. **关系抽取与分类**：
   - 识别实体间的语义关系
   - 关系类型分类和标准化
   - 关系强度和置信度计算
   - 关系验证和过滤

3. **实体链接与消歧**：
   - 将识别的实体链接到知识库
   - 处理实体歧义和同名问题
   - 构建实体的唯一标识符
   - 维护实体的别名和同义词

4. **知识图谱三元组生成**：
   - 生成标准化的RDF三元组
   - 构建实体-关系-实体的知识结构
   - 为Neo4j图数据库准备数据格式
   - 质量检查和数据验证

### 阶段五：内容封装与输出
1. **结构化封装**：将提取结果封装为标准化的文档对象
2. **元数据关联**：关联原始文档信息和提取的元数据
3. **知识图谱数据关联**：关联实体、关系和知识图谱数据
4. **输出准备**：为下游分块模块准备标准化输入

## 模块概述

文档提取模块是RAG系统的数据入口，负责从多种格式的文档中提取结构化内容，为后续的分块和向量化处理提供高质量的文本数据。

## 核心功能架构

### 1. 多格式文档解析引擎

#### 1.1 PDF文档处理
```java
@Component
public class PdfProcessor implements DocumentProcessor {
    
    @Autowired
    private HeheCombOcrClient ocrClient;
    
    public DocumentContent process(InputStream inputStream, ProcessConfig config) {
        PDDocument document = PDDocument.load(inputStream);
        
        if (isTextBasedPdf(document)) {
            return extractTextContent(document);
        } else {
            return extractOcrContent(document);
        }
    }
    
    private DocumentContent extractTextContent(PDDocument document) {
        PDFTextStripper stripper = new PDFTextStripper();
        stripper.setSortByPosition(true);
        
        List<PageContent> pages = new ArrayList<>();
        for (int i = 1; i <= document.getNumberOfPages(); i++) {
            stripper.setStartPage(i);
            stripper.setEndPage(i);
            
            String pageText = stripper.getText(document);
            PageContent page = PageContent.builder()
                .pageNumber(i)
                .content(pageText)
                .metadata(extractPageMetadata(document, i))
                .build();
            pages.add(page);
        }
        
        return DocumentContent.builder()
            .pages(pages)
            .documentType(DocumentType.PDF_TEXT)
            .build();
    }
    
    private DocumentContent extractOcrContent(PDDocument document) {
        List<PageContent> pages = new ArrayList<>();
        
        for (int i = 0; i < document.getNumberOfPages(); i++) {
            PDPage page = document.getPage(i);
            BufferedImage image = renderPageToImage(page);
            
            // 调用合合OCR API
            OcrResult ocrResult = ocrClient.recognizeDocument(image);
            
            PageContent pageContent = PageContent.builder()
                .pageNumber(i + 1)
                .content(ocrResult.getText())
                .layoutInfo(ocrResult.getLayoutInfo())
                .confidence(ocrResult.getConfidence())
                .build();
            pages.add(pageContent);
        }
        
        return DocumentContent.builder()
            .pages(pages)
            .documentType(DocumentType.PDF_OCR)
            .build();
    }
}
```

#### 1.2 Office文档处理
```java
@Component
public class OfficeProcessor implements DocumentProcessor {
    
    public DocumentContent processWord(InputStream inputStream) {
        XWPFDocument document = new XWPFDocument(inputStream);
        
        List<ContentBlock> blocks = new ArrayList<>();
        
        // 处理段落
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            if (!paragraph.getText().trim().isEmpty()) {
                ContentBlock block = ContentBlock.builder()
                    .type(BlockType.PARAGRAPH)
                    .content(paragraph.getText())
                    .style(extractParagraphStyle(paragraph))
                    .build();
                blocks.add(block);
            }
        }
        
        // 处理表格
        for (XWPFTable table : document.getTables()) {
            ContentBlock tableBlock = processTable(table);
            blocks.add(tableBlock);
        }
        
        return DocumentContent.builder()
            .blocks(blocks)
            .documentType(DocumentType.WORD)
            .build();
    }
    
    private ContentBlock processTable(XWPFTable table) {
        List<List<String>> tableData = new ArrayList<>();
        
        for (XWPFTableRow row : table.getRows()) {
            List<String> rowData = new ArrayList<>();
            for (XWPFTableCell cell : row.getTableCells()) {
                rowData.add(cell.getText().trim());
            }
            tableData.add(rowData);
        }
        
        return ContentBlock.builder()
            .type(BlockType.TABLE)
            .content(formatTableAsText(tableData))
            .structuredData(tableData)
            .build();
    }
}
```

#### 1.3 图片文档处理
```java
@Component
public class ImageProcessor implements DocumentProcessor {
    
    @Autowired
    private HeheCombOcrClient ocrClient;
    
    public DocumentContent process(InputStream inputStream, ProcessConfig config) {
        BufferedImage image = ImageIO.read(inputStream);
        
        // 图像预处理
        BufferedImage processedImage = preprocessImage(image);
        
        // OCR识别
        OcrResult ocrResult = ocrClient.recognizeDocument(processedImage);
        
        // 版面分析
        LayoutAnalysisResult layout = ocrClient.analyzeLayout(processedImage);
        
        List<ContentBlock> blocks = new ArrayList<>();
        
        // 按版面区域提取内容
        for (LayoutRegion region : layout.getRegions()) {
            ContentBlock block = ContentBlock.builder()
                .type(mapRegionType(region.getType()))
                .content(region.getText())
                .boundingBox(region.getBoundingBox())
                .confidence(region.getConfidence())
                .build();
            blocks.add(block);
        }
        
        return DocumentContent.builder()
            .blocks(blocks)
            .documentType(DocumentType.IMAGE)
            .layoutInfo(layout)
            .build();
    }
    
    private BufferedImage preprocessImage(BufferedImage image) {
        // 图像增强处理
        // 1. 去噪
        // 2. 对比度调整
        // 3. 倾斜校正
        return ImageEnhancer.enhance(image);
    }
}
```

### 2. 内容预处理管道

#### 2.1 预处理流水线设计
```java
@Component
public class DocumentPreprocessingPipeline {
    
    private final List<PreprocessingStage> stages;
    
    public DocumentPreprocessingPipeline() {
        this.stages = Arrays.asList(
            new FormatDetectionStage(),
            new ContentExtractionStage(),
            new NoiseFilteringStage(),
            new StructureAnalysisStage(),
            new MetadataEnrichmentStage()
        );
    }
    
    public ProcessedDocument process(RawDocument rawDocument) {
        ProcessingContext context = new ProcessingContext(rawDocument);
        
        for (PreprocessingStage stage : stages) {
            try {
                context = stage.process(context);
                logStageCompletion(stage, context);
            } catch (Exception e) {
                handleStageError(stage, context, e);
            }
        }
        
        return context.getProcessedDocument();
    }
}

public interface PreprocessingStage {
    ProcessingContext process(ProcessingContext context) throws ProcessingException;
    String getStageName();
    boolean isRequired();
}
```

#### 2.2 格式检测阶段
```java
@Component
public class FormatDetectionStage implements PreprocessingStage {
    
    @Override
    public ProcessingContext process(ProcessingContext context) {
        RawDocument document = context.getRawDocument();
        
        DocumentFormat format = detectFormat(document);
        DocumentProcessor processor = getProcessor(format);
        
        context.setDocumentFormat(format);
        context.setProcessor(processor);
        
        return context;
    }
    
    private DocumentFormat detectFormat(RawDocument document) {
        String filename = document.getFilename();
        byte[] content = document.getContent();
        
        // 基于文件扩展名
        DocumentFormat formatByExtension = detectByExtension(filename);
        
        // 基于文件头魔数
        DocumentFormat formatByMagicNumber = detectByMagicNumber(content);
        
        // 基于内容特征
        DocumentFormat formatByContent = detectByContentAnalysis(content);
        
        // 综合判断
        return resolveFormatConflict(formatByExtension, formatByMagicNumber, formatByContent);
    }
}
```

#### 2.3 噪声过滤阶段
```java
@Component
public class NoiseFilteringStage implements PreprocessingStage {
    
    @Override
    public ProcessingContext process(ProcessingContext context) {
        DocumentContent content = context.getDocumentContent();
        
        DocumentContent filteredContent = DocumentContent.builder()
            .blocks(filterBlocks(content.getBlocks()))
            .metadata(content.getMetadata())
            .build();
        
        context.setDocumentContent(filteredContent);
        return context;
    }
    
    private List<ContentBlock> filterBlocks(List<ContentBlock> blocks) {
        return blocks.stream()
            .filter(this::isValidBlock)
            .map(this::cleanBlock)
            .collect(Collectors.toList());
    }
    
    private boolean isValidBlock(ContentBlock block) {
        String content = block.getContent();
        
        // 过滤空内容
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        
        // 过滤页眉页脚
        if (isHeaderOrFooter(block)) {
            return false;
        }
        
        // 过滤水印
        if (isWatermark(block)) {
            return false;
        }
        
        // 过滤重复内容
        if (isDuplicate(block)) {
            return false;
        }
        
        return true;
    }
    
    private ContentBlock cleanBlock(ContentBlock block) {
        String content = block.getContent();
        
        // 清理特殊字符
        content = cleanSpecialCharacters(content);
        
        // 标准化空白字符
        content = normalizeWhitespace(content);
        
        // 修复编码问题
        content = fixEncoding(content);
        
        return block.toBuilder()
            .content(content)
            .build();
    }
}
```

### 3. 结构化信息提取

#### 3.1 文档结构分析
```java
@Component
public class StructureAnalysisStage implements PreprocessingStage {
    
    @Override
    public ProcessingContext process(ProcessingContext context) {
        DocumentContent content = context.getDocumentContent();
        
        DocumentStructure structure = analyzeStructure(content);
        
        context.setDocumentStructure(structure);
        return context;
    }
    
    private DocumentStructure analyzeStructure(DocumentContent content) {
        List<ContentBlock> blocks = content.getBlocks();
        
        // 标题层级识别
        List<Heading> headings = extractHeadings(blocks);
        
        // 段落分组
        List<Section> sections = groupIntoSections(blocks, headings);
        
        // 表格识别
        List<Table> tables = extractTables(blocks);
        
        // 列表识别
        List<ListStructure> lists = extractLists(blocks);
        
        return DocumentStructure.builder()
            .headings(headings)
            .sections(sections)
            .tables(tables)
            .lists(lists)
            .build();
    }
    
    private List<Heading> extractHeadings(List<ContentBlock> blocks) {
        List<Heading> headings = new ArrayList<>();
        
        for (ContentBlock block : blocks) {
            if (isHeading(block)) {
                int level = determineHeadingLevel(block);
                Heading heading = Heading.builder()
                    .text(block.getContent())
                    .level(level)
                    .blockIndex(blocks.indexOf(block))
                    .build();
                headings.add(heading);
            }
        }
        
        return headings;
    }
    
    private boolean isHeading(ContentBlock block) {
        String content = block.getContent();
        StyleInfo style = block.getStyle();
        
        // 基于样式判断
        if (style != null && style.isBold() && style.getFontSize() > 12) {
            return true;
        }
        
        // 基于格式模式判断
        if (content.matches("^\\d+\\.\\s+.+") || content.matches("^[A-Z][^.]*$")) {
            return true;
        }
        
        // 基于长度判断
        if (content.length() < 100 && !content.endsWith(".")) {
            return true;
        }
        
        return false;
    }
}
```

#### 3.2 元数据增强
```java
@Component
public class MetadataEnrichmentStage implements PreprocessingStage {
    
    @Override
    public ProcessingContext process(ProcessingContext context) {
        DocumentContent content = context.getDocumentContent();
        DocumentStructure structure = context.getDocumentStructure();
        
        EnrichedMetadata metadata = enrichMetadata(content, structure);
        
        context.setEnrichedMetadata(metadata);
        return context;
    }
    
    private EnrichedMetadata enrichMetadata(DocumentContent content, DocumentStructure structure) {
        return EnrichedMetadata.builder()
            .documentSummary(generateSummary(content))
            .keyPhrases(extractKeyPhrases(content))
            .entities(extractEntities(content))
            .topics(identifyTopics(content))
            .language(detectLanguage(content))
            .readabilityScore(calculateReadability(content))
            .structureComplexity(analyzeComplexity(structure))
            .build();
    }
    
    private List<String> extractKeyPhrases(DocumentContent content) {
        String fullText = content.getBlocks().stream()
            .map(ContentBlock::getContent)
            .collect(Collectors.joining(" "));

        // 使用阿里云NLP提取关键词
        return aliCloudNlpClient.extractKeyPhrases(fullText);
    }
}
```

### 4. 知识图谱数据准备服务

#### 4.1 实体识别与关系抽取服务
```java
@Service
public class KnowledgeGraphDataPreparationService {

    @Autowired
    private EntityRecognitionService entityRecognitionService;

    @Autowired
    private RelationshipExtractionService relationshipExtractionService;

    @Autowired
    private EntityLinkingService entityLinkingService;

    public KnowledgeGraphData prepareKnowledgeGraphData(DocumentContent content, KGDataConfig config) {
        // 1. 实体识别
        List<RecognizedEntity> entities = recognizeEntities(content, config.getEntityConfig());

        // 2. 关系抽取
        List<ExtractedRelationship> relationships = extractRelationships(content, entities, config.getRelationshipConfig());

        // 3. 实体链接
        List<LinkedEntity> linkedEntities = linkEntities(entities, config.getLinkingConfig());

        // 4. 生成知识图谱三元组
        List<KnowledgeTriple> triples = generateTriples(linkedEntities, relationships, config.getTripleConfig());

        return KnowledgeGraphData.builder()
            .entities(linkedEntities)
            .relationships(relationships)
            .triples(triples)
            .build();
    }

    private List<RecognizedEntity> recognizeEntities(DocumentContent content, EntityRecognitionConfig config) {
        List<RecognizedEntity> allEntities = new ArrayList<>();

        for (ContentBlock block : content.getBlocks()) {
            // 使用NER模型识别实体
            List<RecognizedEntity> blockEntities = entityRecognitionService.recognize(
                block.getContent(), config
            );

            // 添加位置信息
            for (RecognizedEntity entity : blockEntities) {
                entity.setBlockId(block.getId());
                entity.setDocumentId(content.getDocumentId());
            }

            allEntities.addAll(blockEntities);
        }

        // 实体去重和合并
        return mergeAndDeduplicateEntities(allEntities, config);
    }

    private List<ExtractedRelationship> extractRelationships(DocumentContent content,
                                                           List<RecognizedEntity> entities,
                                                           RelationshipExtractionConfig config) {
        List<ExtractedRelationship> relationships = new ArrayList<>();

        for (ContentBlock block : content.getBlocks()) {
            // 获取该块中的实体
            List<RecognizedEntity> blockEntities = entities.stream()
                .filter(e -> e.getBlockId().equals(block.getId()))
                .collect(Collectors.toList());

            if (blockEntities.size() >= 2) {
                // 抽取实体间的关系
                List<ExtractedRelationship> blockRelationships = relationshipExtractionService.extract(
                    block.getContent(), blockEntities, config
                );
                relationships.addAll(blockRelationships);
            }
        }

        return relationships;
    }

    private List<LinkedEntity> linkEntities(List<RecognizedEntity> entities, EntityLinkingConfig config) {
        List<LinkedEntity> linkedEntities = new ArrayList<>();

        for (RecognizedEntity entity : entities) {
            // 实体链接到知识库
            LinkedEntity linked = entityLinkingService.link(entity, config);
            if (linked != null) {
                linkedEntities.add(linked);
            }
        }

        return linkedEntities;
    }

    private List<KnowledgeTriple> generateTriples(List<LinkedEntity> entities,
                                                List<ExtractedRelationship> relationships,
                                                TripleGenerationConfig config) {
        List<KnowledgeTriple> triples = new ArrayList<>();

        for (ExtractedRelationship relationship : relationships) {
            // 查找关系的主体和客体实体
            LinkedEntity subject = findEntityById(entities, relationship.getSubjectId());
            LinkedEntity object = findEntityById(entities, relationship.getObjectId());

            if (subject != null && object != null) {
                KnowledgeTriple triple = KnowledgeTriple.builder()
                    .subject(subject.getKnowledgeBaseId())
                    .predicate(relationship.getRelationType())
                    .object(object.getKnowledgeBaseId())
                    .confidence(relationship.getConfidence())
                    .source(relationship.getSource())
                    .build();

                triples.add(triple);
            }
        }

        return triples;
    }
    
    private List<Entity> extractEntities(DocumentContent content) {
        String fullText = content.getBlocks().stream()
            .map(ContentBlock::getContent)
            .collect(Collectors.joining(" "));
        
        // 使用阿里云NLP进行实体识别
        return aliCloudNlpClient.recognizeEntities(fullText);
    }
}
```

## 配置与扩展

### 处理配置
```java
@ConfigurationProperties(prefix = "rag.document.extraction")
@Data
public class ExtractionConfig {
    
    // OCR配置
    private OcrConfig ocr = new OcrConfig();
    
    // 预处理配置
    private PreprocessingConfig preprocessing = new PreprocessingConfig();
    
    // 格式支持配置
    private Map<String, Boolean> supportedFormats = new HashMap<>();
    
    @Data
    public static class OcrConfig {
        private String apiKey;
        private String endpoint;
        private int timeout = 30000;
        private int retryCount = 3;
        private double confidenceThreshold = 0.8;
    }
    
    @Data
    public static class PreprocessingConfig {
        private boolean enableNoiseFiltering = true;
        private boolean enableStructureAnalysis = true;
        private boolean enableMetadataEnrichment = true;
        private int maxContentLength = 1000000;
        private List<String> excludePatterns = new ArrayList<>();
    }
}
```

### 扩展接口
```java
public interface DocumentProcessor {
    DocumentContent process(InputStream inputStream, ProcessConfig config);
    boolean supports(DocumentFormat format);
    int getPriority();
}

public interface ContentEnricher {
    EnrichedContent enrich(DocumentContent content);
    String getEnricherName();
}


```
